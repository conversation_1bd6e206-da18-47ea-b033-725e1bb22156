package com.thyview.network

import com.google.firebase.auth.ktx.auth
import com.google.firebase.ktx.Firebase
import kotlinx.coroutines.runBlocking
import kotlinx.coroutines.tasks.await
import okhttp3.Interceptor
import okhttp3.Response
import timber.log.Timber
import java.io.IOException

/**
 * Interceptor that adds Firebase Auth token to all API requests
 */
class FirebaseAuthInterceptor : Interceptor {
    
    @Throws(IOException::class)
    override fun intercept(chain: Interceptor.Chain): Response {
        val originalRequest = chain.request()

        // Get Firebase Auth token
        val token = getFirebaseAuthToken()

        // Add Authorization header if token is available
        val newRequest = if (token != null) {
            Timber.d("Adding Firebase Auth token to request: ${originalRequest.url}")
            originalRequest.newBuilder()
                .addHeader("Authorization", "Bearer $token")
                .build()
        } else {
            Timber.w("No Firebase Auth token available for request: ${originalRequest.url}")
            originalRequest
        }

        // Log the full request
        Timber.d("Request URL: ${newRequest.url}")
        Timber.d("Request Headers: ${newRequest.headers}")

        val response = chain.proceed(newRequest)

        // Log the response
        Timber.d("Response Code: ${response.code}")
        Timber.d("Response Headers: ${response.headers}")
        if (!response.isSuccessful) {
            Timber.e("Request failed with code: ${response.code}, message: ${response.message}")
        }

        return response
    }
    
    /**
     * Get Firebase Auth token synchronously
     * Note: This blocks the thread, but it's necessary for the interceptor
     */
    private fun getFirebaseAuthToken(): String? {
        return try {
            val currentUser = Firebase.auth.currentUser
            if (currentUser != null) {
                Timber.d("Getting Firebase Auth token for user: ${currentUser.uid}")
                runBlocking {
                    val tokenResult = currentUser.getIdToken(false).await()
                    val token = tokenResult.token
                    if (token != null) {
                        Timber.d("Successfully retrieved Firebase Auth token (length: ${token.length})")
                        Timber.d("Firebase Auth token (length: ${token}")
                        // Log first and last few characters for debugging (don't log full token)
                        if (token.length > 10) {
                            Timber.d("Token preview: ${token.take(10)}...${token.takeLast(10)}")
                        }
                    } else {
                        Timber.w("Firebase Auth token is null")
                    }
                    token
                }
            } else {
                Timber.w("No authenticated user found in Firebase Auth")
                null
            }
        } catch (e: Exception) {
            Timber.e(e, "Failed to get Firebase Auth token: ${e.message}")
            null
        }
    }
}
