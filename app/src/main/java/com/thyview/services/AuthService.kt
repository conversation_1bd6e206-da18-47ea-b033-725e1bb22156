package com.thyview.services

import android.content.Context
import android.content.Intent
import android.net.Uri
import com.google.firebase.auth.ktx.auth
import com.google.firebase.ktx.Firebase
import com.thyview.glide.MyAppGlideModule
import com.thyview.deepLinking.PromotionsHandler
import com.thyview.models.UserObject
import com.thyview.storage.prefs.AccountPreferences
import com.thyview.utils.Constants
import kotlinx.coroutines.tasks.await
import timber.log.Timber
import java.util.Locale

class AuthService {

    companion object {

        fun cleanup(context: Context?) {

            val influencerId = AccountPreferences.getInstance(context).getStringValue(Constants.influencerId, "")
            val influencerName = AccountPreferences.getInstance(context).getStringValue(Constants.influencerName, "")
            val installSource = AccountPreferences.getInstance(context).getStringValue(Constants.installSource, "")

            AccountPreferences.getInstance(context).clear()
            UserObject.cleanup()

            if (!influencerId.isNullOrEmpty() && context != null) {
                PromotionsHandler.handleInfluencerSignups(context, influencerId, influencerName)
            }

            if (!installSource.isNullOrEmpty() && context != null) {
                //PromotionsHandler.handleBranchCampaignSignups(context, installSource)
            }

            context?.let {
                MyAppGlideModule.clearCache(it)
            }
        }

        fun isUserAuthenticated(): Boolean {
            return Firebase.auth.currentUser != null
        }

        /**
         * Get Firebase Auth token
         */
        suspend fun getFirebaseAuthToken(): String? {
            return try {
                val currentUser = Firebase.auth.currentUser
                if (currentUser != null) {
                    Timber.d("Getting Firebase Auth token for user: ${currentUser.uid}")
                    val tokenResult = currentUser.getIdToken(false).await()
                    val token = tokenResult.token
                    if (token != null) {
                        Timber.d("Successfully retrieved Firebase Auth token")
                    } else {
                        Timber.w("Firebase Auth token is null")
                    }
                    token
                } else {
                    Timber.w("No authenticated user found")
                    null
                }
            } catch (e: Exception) {
                Timber.e(e, "Failed to get Firebase Auth token")
                null
            }
        }

        /**
         * Get current user's email from Firebase Auth
         */
        fun getCurrentUserEmail(): String? {
            return Firebase.auth.currentUser?.email
        }

        /**
         * Get current user's display name from Firebase Auth
         */
        fun getCurrentUserDisplayName(): String? {
            return Firebase.auth.currentUser?.displayName
        }

        /**
         * Get current user's profile image URL from Firebase Auth
         */
        fun getCurrentUserPhotoUrl(): String? {
            return Firebase.auth.currentUser?.photoUrl?.toString()
        }

        /**
         * Get current user's external ID (Firebase UID)
         */
        fun getCurrentUserExternalId(): String? {
            return Firebase.auth.currentUser?.uid
        }

        /**
         * Get device region/locale
         */
        fun getDeviceRegion(): String {
            return Locale.getDefault().country.ifEmpty { "US" }
        }

        /**
         * Create email intent for contact us functionality
         */
        fun createContactEmailIntent(context: Context, externalId: String?): Intent {
            val emailIntent = Intent(Intent.ACTION_SENDTO).apply {
                data = Uri.parse("mailto:")
                putExtra(Intent.EXTRA_EMAIL, arrayOf("<EMAIL>"))
                putExtra(Intent.EXTRA_SUBJECT, "ThyView Support - User ID: ${externalId ?: "Unknown"}")
                putExtra(Intent.EXTRA_TEXT, "Hi ThyView Support Team,\n\n[Please describe your issue here]\n\nUser External ID: ${externalId ?: "Unknown"}")
            }
            return Intent.createChooser(emailIntent, "Send Email")
        }

        /**
         * Open URL in device web browser
         */
        fun openUrlInBrowser(context: Context, url: String) {
            try {
                val intent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
                context.startActivity(intent)
            } catch (e: Exception) {
                Timber.e(e, "Failed to open URL in browser: $url")
            }
        }

        /**
         * Logout user and clean up
         */
        fun logout(context: Context) {
            Firebase.auth.signOut()
            cleanup(context)
        }
    }
}