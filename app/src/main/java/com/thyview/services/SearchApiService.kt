package com.thyview.services

import com.thyview.models.search.*
import retrofit2.http.*

/**
 * REST API interface for search and movie/TV/person details operations
 */
interface SearchApiService {
    
    /**
     * Unified search endpoint that returns movies, TV shows, persons, and genres
     */
    @GET("movie/search")
    suspend fun search(
        @Query("q") query: String
    ): SearchResponse
    
    /**
     * Get detailed information about a movie
     */
    @GET("movie/details/movie/{movieId}")
    suspend fun getMovieDetails(
        @Path("movieId") movieId: Int
    ): MovieDetails
    
    /**
     * Get detailed information about a TV show
     */
    @GET("movie/details/tv/{tvShowId}")
    suspend fun getTVDetails(
        @Path("tvShowId") tvShowId: Int
    ): TVDetails
    
    /**
     * Get detailed information about a person
     */
    @GET("movie/details/person/{personId}")
    suspend fun getPersonDetails(
        @Path("personId") personId: Int
    ): PersonDetails
    
    /**
     * Get available streaming services for a movie/TV show (mock implementation)
     */
    @GET("movie/streaming/{contentType}/{contentId}")
    suspend fun getStreamingServices(
        @Path("contentType") contentType: String, // "movie" or "tv"
        @Path("contentId") contentId: Int
    ): List<StreamingService>
    
    /**
     * Add/remove item from watchlist (mock implementation)
     */
    @POST("movie/watchlist")
    suspend fun toggleWatchlist(
        @Body request: WatchlistToggleRequest
    ): WatchlistResponse
    
    /**
     * Check if item is in watchlist (mock implementation)
     */
    @GET("movie/watchlist/{contentType}/{contentId}")
    suspend fun isInWatchlist(
        @Path("contentType") contentType: String,
        @Path("contentId") contentId: Int
    ): WatchlistResponse
}

/**
 * Request model for watchlist operations
 */
data class WatchlistToggleRequest(
    val contentId: Int,
    val contentType: String, // "movie" or "tv"
    val action: String // "add" or "remove"
)
