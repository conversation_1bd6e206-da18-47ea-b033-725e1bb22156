package com.thyview.services

import com.google.gson.Gson
import com.thyview.models.search.*
import timber.log.Timber
import javax.inject.Inject
import javax.inject.Singleton

/**
 * Service for handling search operations and content details
 */
@Singleton
class SearchService @Inject constructor(
    private val searchApiService: SearchApiService,
    private val gson: <PERSON><PERSON>
) {

    val useMockData = false

    // Mock search results for different queries
    private val mockSearchResults = mapOf(
        "avengers" to SearchResponse(
            query = "avengers",
            type = "unified",
            results = listOf(
                SearchResult(
                    id = 24428,
                    type = "movie",
                    title = "The Avengers",
                    releaseYear = 2012,
                    poster = "https://image.tmdb.org/t/p/w500/RYMX2wcKCBAr24UyPD7xwmjaTn.jpg",
                    overview = "When an unexpected enemy emerges and threatens global safety and security, <PERSON>, director of the international peacekeeping agency known as S.H.I.E.L.D., finds himself in need of a team to pull the world back from the brink of disaster.",
                    runtime = 143,
                    rating = 7.7,
                    genres = listOf("Action", "Adventure", "Science Fiction"),
                    director = "<PERSON><PERSON> Whedon",
                    cast = listOf(
                        CastMember(1, "<PERSON>", "<PERSON> / Iron Man", "https://image.tmdb.org/t/p/w500/5qHNjhtjMD4YWH3UP0rm4tKwxCL.jpg"),
                        CastMember(2, "Chris Evans", "Steve Rogers / Captain America", "https://image.tmdb.org/t/p/w500/3bOGNsHlrswhyW79uvIHH1V43JI.jpg"),
                        CastMember(3, "Scarlett Johansson", "Natasha Romanoff / Black Widow", "https://image.tmdb.org/t/p/w500/6NsMbJXRlDZuDzatN2akFdGuTvx.jpg")
                    )
                ),
                SearchResult(
                    id = 299536,
                    type = "movie",
                    title = "Avengers: Infinity War",
                    releaseYear = 2018,
                    poster = "https://image.tmdb.org/t/p/w500/7WsyChQLEftFiDOVTGkv3hFpyyt.jpg",
                    overview = "As the Avengers and their allies have continued to protect the world from threats too large for any one hero to handle, a new danger has emerged from the cosmic shadows: Thanos.",
                    runtime = 149,
                    rating = 8.3,
                    genres = listOf("Adventure", "Action", "Science Fiction"),
                    director = "Anthony Russo, Joe Russo"
                )
            ),
            meta = SearchMeta("req_001", "2024-01-15T10:30:00Z")
        ),
        "stranger things" to SearchResponse(
            query = "stranger things",
            type = "unified",
            results = listOf(
                SearchResult(
                    id = 66732,
                    type = "tv",
                    name = "Stranger Things",
                    releaseYear = 2016,
                    poster = "https://image.tmdb.org/t/p/w500/x2LSRK2Cm7MZhjluni1msVJ3wDF.jpg",
                    overview = "When a young boy vanishes, a small town uncovers a mystery involving secret experiments, terrifying supernatural forces, and one strange little girl.",
                    rating = 8.6,
                    genres = listOf("Sci-Fi & Fantasy", "Mystery", "Drama"),
                    seasons = 4,
                    episodes = 42,
                    status = "Ended",
                    creators = listOf("The Duffer Brothers"),
                    cast = listOf(
                        CastMember(4, "Millie Bobby Brown", "Eleven", "https://image.tmdb.org/t/p/w500/qZtAf4Z1lazGQoYVXiHOrvLr5lI.jpg"),
                        CastMember(5, "Finn Wolfhard", "Mike Wheeler", "https://image.tmdb.org/t/p/w500/sKzTke6ulpZYg8ZXGqzJpJsHZs8.jpg")
                    )
                )
            ),
            meta = SearchMeta("req_002", "2024-01-15T10:31:00Z")
        ),
        "leonardo dicaprio" to SearchResponse(
            query = "leonardo dicaprio",
            type = "unified",
            results = listOf(
                SearchResult(
                    id = 6193,
                    type = "person",
                    name = "Leonardo DiCaprio",
                    profile = "https://image.tmdb.org/t/p/w500/wo2hJpn04vbtmh0B9utCFdsQhxM.jpg",
                    knownForDepartment = "Acting",
                    biography = "Leonardo Wilhelm DiCaprio is an American actor and film producer. Known for his work in biopics and period films, DiCaprio has received numerous accolades throughout his career.",
                    birthday = "1974-11-11",
                    placeOfBirth = "Los Angeles, California, USA",
                    knownFor = listOf(
                        KnownForItem(27205, "Inception", "movie"),
                        KnownForItem(597, "Titanic", "movie"),
                        KnownForItem(453405, "The Revenant", "movie")
                    )
                )
            ),
            meta = SearchMeta("req_003", "2024-01-15T10:32:00Z")
        ),
        "action" to SearchResponse(
            query = "action",
            type = "unified",
            results = listOf(
                SearchResult(
                    id = 28,
                    type = "genre",
                    name = "Action",
                    title = "Mad Max: Fury Road",
                    releaseYear = 2015,
                    poster = "https://image.tmdb.org/t/p/w500/hA2ple9q4qnwxp3hKVNhroipsir.jpg",
                    overview = "An apocalyptic story set in the furthest reaches of our planet, in a stark desert landscape where humanity is broken.",
                    rating = 7.6,
                    genres = listOf("Action", "Adventure", "Science Fiction")
                ),
                SearchResult(
                    id = 155,
                    type = "genre",
                    name = "Action",
                    title = "The Dark Knight",
                    releaseYear = 2008,
                    poster = "https://image.tmdb.org/t/p/w500/qJ2tW6WMUDux911r6m7haRef0WH.jpg",
                    overview = "Batman raises the stakes in his war on crime with the help of Lt. Jim Gordon and District Attorney Harvey Dent.",
                    rating = 9.0,
                    genres = listOf("Drama", "Action", "Crime", "Thriller")
                )
            ),
            meta = SearchMeta("req_004", "2024-01-15T10:33:00Z")
        ),
        "batman" to SearchResponse(
            query = "batman",
            type = "unified",
            results = listOf(
                SearchResult(
                    id = 155,
                    type = "movie",
                    title = "The Dark Knight",
                    releaseYear = 2008,
                    poster = "https://image.tmdb.org/t/p/w500/qJ2tW6WMUDux911r6m7haRef0WH.jpg",
                    overview = "Batman raises the stakes in his war on crime with the help of Lt. Jim Gordon and District Attorney Harvey Dent.",
                    runtime = 152,
                    rating = 9.0,
                    genres = listOf("Drama", "Action", "Crime", "Thriller"),
                    director = "Christopher Nolan"
                ),
                SearchResult(
                    id = 414906,
                    type = "movie",
                    title = "The Batman",
                    releaseYear = 2022,
                    poster = "https://image.tmdb.org/t/p/w500/b0PlSFdDwbyK0cf5RxwDpaOJQvQ.jpg",
                    overview = "In his second year of fighting crime, Batman uncovers corruption in Gotham City that connects to his own family while facing a serial killer known as the Riddler.",
                    runtime = 176,
                    rating = 7.8,
                    genres = listOf("Crime", "Mystery", "Thriller"),
                    director = "Matt Reeves"
                )
            ),
            meta = SearchMeta("req_005", "2024-01-15T10:34:00Z")
        ),
        "marvel" to SearchResponse(
            query = "marvel",
            type = "unified",
            results = listOf(
                SearchResult(
                    id = 24428,
                    type = "movie",
                    title = "The Avengers",
                    releaseYear = 2012,
                    poster = "https://image.tmdb.org/t/p/w500/RYMX2wcKCBAr24UyPD7xwmjaTn.jpg",
                    overview = "When an unexpected enemy emerges and threatens global safety and security, Nick Fury finds himself in need of a team.",
                    runtime = 143,
                    rating = 7.7,
                    genres = listOf("Action", "Adventure", "Science Fiction"),
                    director = "Joss Whedon"
                ),
                SearchResult(
                    id = 1726,
                    type = "movie",
                    title = "Iron Man",
                    releaseYear = 2008,
                    poster = "https://image.tmdb.org/t/p/w500/78lPtwv72eTNqFW9COBYI0dWDJa.jpg",
                    overview = "After being held captive in an Afghan cave, billionaire engineer Tony Stark creates a unique weaponized suit of armor to fight evil.",
                    runtime = 126,
                    rating = 7.6,
                    genres = listOf("Action", "Science Fiction", "Adventure"),
                    director = "Jon Favreau"
                )
            ),
            meta = SearchMeta("req_006", "2024-01-15T10:35:00Z")
        )
    )

    // Mock movie details
    private val mockMovieDetails = mapOf(
        24428 to MovieDetails(
            id = 24428,
            title = "The Avengers",
            originalTitle = "The Avengers",
            tagline = "Some assembly required.",
            year = 2012,
            releaseDate = "2012-04-25",
            rating = 7.7,
            voteCount = 28847,
            popularity = 118.864,
            poster = "https://image.tmdb.org/t/p/w500/RYMX2wcKCBAr24UyPD7xwmjaTn.jpg",
            backdrop = "https://image.tmdb.org/t/p/w1280/9BBTo63ANSmhC4e6r62OJFuK2GL.jpg",
            overview = "When an unexpected enemy emerges and threatens global safety and security, Nick Fury, director of the international peacekeeping agency known as S.H.I.E.L.D., finds himself in need of a team to pull the world back from the brink of disaster. Spanning the globe, a daring recruitment effort begins!",
            runtime = 143,
            budget = 220000000,
            revenue = 1518815515,
            status = "Released",
            adult = false,
            originalLanguage = "en",
            spokenLanguages = listOf(
                SpokenLanguage("en", "English"),
                SpokenLanguage("hi", "हिन्दी"),
                SpokenLanguage("ru", "Pусский")
            ),
            genres = listOf(
                Genre(28, "Action"),
                Genre(12, "Adventure"),
                Genre(878, "Science Fiction")
            ),
            productionCompanies = listOf(
                ProductionCompany(420, "Marvel Studios", "https://image.tmdb.org/t/p/w500/hUzeosd33nzE5MCNsZxCGEKTXaQ.png", "US")
            ),
            productionCountries = listOf(
                ProductionCountry("US", "United States of America")
            ),
            cast = listOf(
                DetailedCastMember(3223, "Robert Downey Jr.", "Tony Stark / Iron Man", "52fe4751c3a36847f8024f49", 0, "https://image.tmdb.org/t/p/w500/5qHNjhtjMD4YWH3UP0rm4tKwxCL.jpg", "Acting", 58.132),
                DetailedCastMember(16828, "Chris Evans", "Steve Rogers / Captain America", "52fe4751c3a36847f8024f55", 1, "https://image.tmdb.org/t/p/w500/3bOGNsHlrswhyW79uvIHH1V43JI.jpg", "Acting", 49.388),
                DetailedCastMember(1245, "Scarlett Johansson", "Natasha Romanoff / Black Widow", "52fe4751c3a36847f8024f5b", 2, "https://image.tmdb.org/t/p/w500/6NsMbJXRlDZuDzatN2akFdGuTvx.jpg", "Acting", 56.61)
            ),
            crew = listOf(
                CrewMember(19271, "Joss Whedon", "Director", "Directing", "52fe4751c3a36847f8024f65", "https://image.tmdb.org/t/p/w500/dTiVsuaTVTeGmvkhcyJvKp2A5kr.jpg", "Directing", 4.294)
            ),
            director = "Joss Whedon",
            directors = listOf(
                PersonSummary(19271, "Joss Whedon", "Director", "https://image.tmdb.org/t/p/w500/dTiVsuaTVTeGmvkhcyJvKp2A5kr.jpg")
            ),
            writers = listOf(
                PersonSummary(19271, "Joss Whedon", "Screenplay", "https://image.tmdb.org/t/p/w500/dTiVsuaTVTeGmvkhcyJvKp2A5kr.jpg")
            ),
            producers = listOf(
                PersonSummary(19271, "Kevin Feige", "Producer", "https://image.tmdb.org/t/p/w500/kDnPTaGcVHzwItDbeWWmAA3dnXg.jpg")
            ),
            similar = listOf(
                SimilarItem(299536, "Avengers: Infinity War", 2018, "https://image.tmdb.org/t/p/w500/7WsyChQLEftFiDOVTGkv3hFpyyt.jpg", 8.3, "As the Avengers and their allies have continued to protect the world from threats too large for any one hero to handle, a new danger has emerged from the cosmic shadows: Thanos.")
            ),
            recommendations = listOf(
                SimilarItem(299537, "Avengers: Endgame", 2019, "https://image.tmdb.org/t/p/w500/or06FN3Dka5tukK1e9sl16pB3iy.jpg", 8.3, "After the devastating events of Avengers: Infinity War, the universe is in ruins due to the efforts of the Mad Titan, Thanos.")
            ),
            imdbId = "tt0848228",
            homepage = "https://www.marvel.com/movies/the-avengers",
            belongsToCollection = Collection(86311, "The Avengers Collection", "https://image.tmdb.org/t/p/w500/yFSIUVTCvgYrpalUktulvk3Gi5Y.jpg", "https://image.tmdb.org/t/p/w1280/zuW6fOiusv4X9nnW3paHGfXcSll.jpg")
        ),
        299536 to MovieDetails(
            id = 299536,
            title = "Avengers: Infinity War",
            originalTitle = "Avengers: Infinity War",
            tagline = "An entire universe. Once and for all.",
            year = 2018,
            releaseDate = "2018-04-25",
            rating = 8.3,
            voteCount = 28847,
            popularity = 118.864,
            poster = "https://image.tmdb.org/t/p/w500/7WsyChQLEftFiDOVTGkv3hFpyyt.jpg",
            backdrop = "https://image.tmdb.org/t/p/w1280/lmZFxXgJE3vgrciwuDib0N8CfQo.jpg",
            overview = "As the Avengers and their allies have continued to protect the world from threats too large for any one hero to handle, a new danger has emerged from the cosmic shadows: Thanos. A despot of intergalactic infamy, his goal is to collect all six Infinity Stones, artifacts of unimaginable power, and use them to inflict his twisted will on all of reality.",
            runtime = 149,
            budget = *********,
            revenue = 2048359754,
            status = "Released",
            adult = false,
            originalLanguage = "en",
            spokenLanguages = listOf(SpokenLanguage("en", "English")),
            genres = listOf(
                Genre(12, "Adventure"),
                Genre(28, "Action"),
                Genre(878, "Science Fiction")
            ),
            productionCompanies = listOf(
                ProductionCompany(420, "Marvel Studios", "https://image.tmdb.org/t/p/w500/hUzeosd33nzE5MCNsZxCGEKTXaQ.png", "US")
            ),
            productionCountries = listOf(
                ProductionCountry("US", "United States of America")
            ),
            cast = listOf(
                DetailedCastMember(3223, "Robert Downey Jr.", "Tony Stark / Iron Man", "52fe4751c3a36847f8024f49", 0, "https://image.tmdb.org/t/p/w500/5qHNjhtjMD4YWH3UP0rm4tKwxCL.jpg", "Acting", 58.132),
                DetailedCastMember(16828, "Chris Evans", "Steve Rogers / Captain America", "52fe4751c3a36847f8024f55", 1, "https://image.tmdb.org/t/p/w500/3bOGNsHlrswhyW79uvIHH1V43JI.jpg", "Acting", 49.388)
            ),
            crew = listOf(
                CrewMember(19271, "Anthony Russo", "Director", "Directing", "52fe4751c3a36847f8024f65", "https://image.tmdb.org/t/p/w500/dTiVsuaTVTeGmvkhcyJvKp2A5kr.jpg", "Directing", 4.294),
                CrewMember(19272, "Joe Russo", "Director", "Directing", "52fe4751c3a36847f8024f66", "https://image.tmdb.org/t/p/w500/dTiVsuaTVTeGmvkhcyJvKp2A5kr.jpg", "Directing", 4.294)
            ),
            director = "Anthony Russo, Joe Russo",
            directors = listOf(
                PersonSummary(19271, "Anthony Russo", "Director", "https://image.tmdb.org/t/p/w500/dTiVsuaTVTeGmvkhcyJvKp2A5kr.jpg"),
                PersonSummary(19272, "Joe Russo", "Director", "https://image.tmdb.org/t/p/w500/dTiVsuaTVTeGmvkhcyJvKp2A5kr.jpg")
            ),
            writers = listOf(
                PersonSummary(19271, "Christopher Markus", "Screenplay", "https://image.tmdb.org/t/p/w500/dTiVsuaTVTeGmvkhcyJvKp2A5kr.jpg")
            ),
            producers = listOf(
                PersonSummary(19271, "Kevin Feige", "Producer", "https://image.tmdb.org/t/p/w500/kDnPTaGcVHzwItDbeWWmAA3dnXg.jpg")
            ),
            similar = listOf(
                SimilarItem(24428, "The Avengers", 2012, "https://image.tmdb.org/t/p/w500/RYMX2wcKCBAr24UyPD7xwmjaTn.jpg", 7.7, "When an unexpected enemy emerges and threatens global safety and security, Nick Fury finds himself in need of a team.")
            ),
            recommendations = listOf(
                SimilarItem(299537, "Avengers: Endgame", 2019, "https://image.tmdb.org/t/p/w500/or06FN3Dka5tukK1e9sl16pB3iy.jpg", 8.3, "After the devastating events of Avengers: Infinity War, the universe is in ruins.")
            ),
            imdbId = "tt4154756",
            homepage = "https://www.marvel.com/movies/avengers-infinity-war",
            belongsToCollection = Collection(86311, "The Avengers Collection", "https://image.tmdb.org/t/p/w500/yFSIUVTCvgYrpalUktulvk3Gi5Y.jpg", "https://image.tmdb.org/t/p/w1280/zuW6fOiusv4X9nnW3paHGfXcSll.jpg")
        ),
        155 to MovieDetails(
            id = 155,
            title = "The Dark Knight",
            originalTitle = "The Dark Knight",
            tagline = "Welcome to a world without rules.",
            year = 2008,
            releaseDate = "2008-07-16",
            rating = 9.0,
            voteCount = 32000,
            popularity = 123.456,
            poster = "https://image.tmdb.org/t/p/w500/qJ2tW6WMUDux911r6m7haRef0WH.jpg",
            backdrop = "https://image.tmdb.org/t/p/w1280/hqkIcbrOHL86UncnHIsHVcVmzue.jpg",
            overview = "Batman raises the stakes in his war on crime. With the help of Lt. Jim Gordon and District Attorney Harvey Dent, Batman sets out to dismantle the remaining criminal organizations that plague the streets. The partnership proves to be effective, but they soon find themselves prey to a reign of chaos unleashed by a rising criminal mastermind known to the terrified citizens of Gotham as the Joker.",
            runtime = 152,
            budget = *********,
            revenue = 1004558444,
            status = "Released",
            adult = false,
            originalLanguage = "en",
            spokenLanguages = listOf(SpokenLanguage("en", "English")),
            genres = listOf(
                Genre(18, "Drama"),
                Genre(28, "Action"),
                Genre(80, "Crime"),
                Genre(53, "Thriller")
            ),
            productionCompanies = listOf(
                ProductionCompany(9996, "Warner Bros. Pictures", "https://image.tmdb.org/t/p/w500/wdrCwmRnLFJhEoH8GSfymY85KHT.png", "US"),
                ProductionCompany(9993, "DC Entertainment", "https://image.tmdb.org/t/p/w500/2Tc1P3Ac8M479naPp1kYT3izLS5.png", "US")
            ),
            productionCountries = listOf(
                ProductionCountry("US", "United States of America"),
                ProductionCountry("GB", "United Kingdom")
            ),
            cast = listOf(
                DetailedCastMember(3894, "Christian Bale", "Bruce Wayne / Batman", "52fe4781c3a36847f81218a7", 0, "https://image.tmdb.org/t/p/w500/vecCvACI2QhSE5fOoOXOqhOYgcN.jpg", "Acting", 45.123),
                DetailedCastMember(1579, "Heath Ledger", "Joker", "52fe4781c3a36847f81218ab", 1, "https://image.tmdb.org/t/p/w500/5Y9HnYYa9jF4NunY9lSgJGjSe8E.jpg", "Acting", 12.345),
                DetailedCastMember(64, "Gary Oldman", "James Gordon", "52fe4781c3a36847f81218af", 2, "https://image.tmdb.org/t/p/w500/2v9FVVBUrrkW2m3QOcYkuhq9A6o.jpg", "Acting", 34.567)
            ),
            crew = listOf(
                CrewMember(525, "Christopher Nolan", "Director", "Directing", "52fe4781c3a36847f81218c1", "https://image.tmdb.org/t/p/w500/xuAIuYSmsUzKlUMBFGVZaWsY3DZ.jpg", "Directing", 15.678)
            ),
            director = "Christopher Nolan",
            directors = listOf(
                PersonSummary(525, "Christopher Nolan", "Director", "https://image.tmdb.org/t/p/w500/xuAIuYSmsUzKlUMBFGVZaWsY3DZ.jpg")
            ),
            writers = listOf(
                PersonSummary(525, "Christopher Nolan", "Screenplay", "https://image.tmdb.org/t/p/w500/xuAIuYSmsUzKlUMBFGVZaWsY3DZ.jpg"),
                PersonSummary(7467, "Jonathan Nolan", "Screenplay", "https://image.tmdb.org/t/p/w500/6J8Q0aGNFfmVBkTZTnU5YrjYOmE.jpg")
            ),
            producers = listOf(
                PersonSummary(525, "Christopher Nolan", "Producer", "https://image.tmdb.org/t/p/w500/xuAIuYSmsUzKlUMBFGVZaWsY3DZ.jpg")
            ),
            similar = listOf(
                SimilarItem(414906, "The Batman", 2022, "https://image.tmdb.org/t/p/w500/b0PlSFdDwbyK0cf5RxwDpaOJQvQ.jpg", 7.8, "In his second year of fighting crime, Batman uncovers corruption in Gotham City.")
            ),
            recommendations = listOf(
                SimilarItem(49026, "The Dark Knight Rises", 2012, "https://image.tmdb.org/t/p/w500/hr0L2aueqlP2BYUblTTjmtn0hw4.jpg", 7.6, "Following the death of District Attorney Harvey Dent, Batman assumes responsibility for Dent's crimes.")
            ),
            imdbId = "tt0468569",
            homepage = null,
            belongsToCollection = Collection(263, "The Dark Knight Collection", "https://image.tmdb.org/t/p/w500/bqS2lMgGkuodIXtDILFWTSWDDpa.jpg", "https://image.tmdb.org/t/p/w1280/xfKHcEm3rJfXC1wLCOmKr3Llf5l.jpg")
        ),
        1726 to MovieDetails(
            id = 1726,
            title = "Iron Man",
            originalTitle = "Iron Man",
            tagline = "Heroes aren't born. They're built.",
            year = 2008,
            releaseDate = "2008-04-30",
            rating = 7.6,
            voteCount = 24000,
            popularity = 78.021,
            poster = "https://image.tmdb.org/t/p/w500/78lPtwv72eTNqFW9COBYI0dWDJa.jpg",
            backdrop = "https://image.tmdb.org/t/p/w1280/cyecB7godJ6kNHGONFjUyVN9OX5.jpg",
            overview = "After being held captive in an Afghan cave, billionaire engineer Tony Stark creates a unique weaponized suit of armor to fight evil.",
            runtime = 126,
            budget = *********,
            revenue = *********,
            status = "Released",
            adult = false,
            originalLanguage = "en",
            spokenLanguages = listOf(SpokenLanguage("en", "English")),
            genres = listOf(
                Genre(28, "Action"),
                Genre(878, "Science Fiction"),
                Genre(12, "Adventure")
            ),
            productionCompanies = listOf(
                ProductionCompany(420, "Marvel Studios", "https://image.tmdb.org/t/p/w500/hUzeosd33nzE5MCNsZxCGEKTXaQ.png", "US")
            ),
            productionCountries = listOf(
                ProductionCountry("US", "United States of America")
            ),
            cast = listOf(
                DetailedCastMember(3223, "Robert Downey Jr.", "Tony Stark / Iron Man", "52fe4302c3a36847f8019f9b", 0, "https://image.tmdb.org/t/p/w500/5qHNjhtjMD4YWH3UP0rm4tKwxCL.jpg", "Acting", 58.132),
                DetailedCastMember(1896, "Gwyneth Paltrow", "Pepper Potts", "52fe4302c3a36847f8019f9f", 1, "https://image.tmdb.org/t/p/w500/***************************.jpg", "Acting", 12.345),
                DetailedCastMember(1117, "Jeff Bridges", "Obadiah Stane / Iron Monger", "52fe4302c3a36847f8019fa3", 2, "https://image.tmdb.org/t/p/w500/xms1RAY6q7EE3dPyPiZNNYYhSWU.jpg", "Acting", 8.765)
            ),
            crew = listOf(
                CrewMember(15277, "Jon Favreau", "Director", "Directing", "52fe4302c3a36847f8019fb5", "https://image.tmdb.org/t/p/w500/85fqKRNGRbBHHal6WdCYbwwn2dU.jpg", "Directing", 4.567)
            ),
            director = "Jon Favreau",
            directors = listOf(
                PersonSummary(15277, "Jon Favreau", "Director", "https://image.tmdb.org/t/p/w500/85fqKRNGRbBHHal6WdCYbwwn2dU.jpg")
            ),
            writers = listOf(
                PersonSummary(15277, "Mark Fergus", "Screenplay", "https://image.tmdb.org/t/p/w500/85fqKRNGRbBHHal6WdCYbwwn2dU.jpg")
            ),
            producers = listOf(
                PersonSummary(19271, "Kevin Feige", "Producer", "https://image.tmdb.org/t/p/w500/kDnPTaGcVHzwItDbeWWmAA3dnXg.jpg")
            ),
            similar = listOf(
                SimilarItem(24428, "The Avengers", 2012, "https://image.tmdb.org/t/p/w500/RYMX2wcKCBAr24UyPD7xwmjaTn.jpg", 7.7, "When an unexpected enemy emerges and threatens global safety and security.")
            ),
            recommendations = listOf(
                SimilarItem(1927, "Iron Man 2", 2010, "https://image.tmdb.org/t/p/w500/6WBeq4fCfn7AN0o21W9qNcRF2l9.jpg", 6.8, "With the world now aware of his dual life as the armored superhero Iron Man.")
            ),
            imdbId = "tt0371746",
            homepage = "https://www.marvel.com/movies/iron-man",
            belongsToCollection = Collection(131292, "Iron Man Collection", "https://image.tmdb.org/t/p/w500/fhHT3YOjvNiSBP3hqO5ez2pZRDy.jpg", "https://image.tmdb.org/t/p/w1280/mGLWK1VmBsw3uWdwjGWqKHb5Oqy.jpg")
        )
    )

    // Mock TV details
    private val mockTVDetails = mapOf(
        66732 to TVDetails(
            id = 66732,
            name = "Stranger Things",
            year = 2016,
            poster = "https://image.tmdb.org/t/p/w500/x2LSRK2Cm7MZhjluni1msVJ3wDF.jpg",
            overview = "When a young boy vanishes, a small town uncovers a mystery involving secret experiments, terrifying supernatural forces, and one strange little girl.",
            rating = 8.6,
            genres = listOf(
                Genre(10765, "Sci-Fi & Fantasy"),
                Genre(9648, "Mystery"),
                Genre(18, "Drama")
            ),
            seasons = 4,
            episodes = 42,
            status = "Ended",
            creators = listOf(Creator(1, "The Duffer Brothers", "credit_duffer", null)),
            cast = listOf(
                DetailedCastMember(4, "Millie Bobby Brown", "Eleven", "credit1", 0, "https://image.tmdb.org/t/p/w500/qZtAf4Z1lazGQoYVXiHOrvLr5lI.jpg", "Acting", 8.5),
                DetailedCastMember(5, "Finn Wolfhard", "Mike Wheeler", "credit2", 1, "https://image.tmdb.org/t/p/w500/sKzTke6ulpZYg8ZXGqzJpJsHZs8.jpg", "Acting", 7.2),
                DetailedCastMember(6, "Gaten Matarazzo", "Dustin Henderson", "credit3", 2, "https://image.tmdb.org/t/p/w500/x78BtYHElirO7Iw8bL4m6E5YM8p.jpg", "Acting", 6.8),
                DetailedCastMember(7, "Caleb McLaughlin", "Lucas Sinclair", "credit4", 3, "https://image.tmdb.org/t/p/w500/oP6GrGiUbf0kKUBOQCYNHaaJdTi.jpg", "Acting", 6.5)
            )
        )
    )

    // Mock person details
    private val mockPersonDetails = mapOf(
        6193 to PersonDetails(
            id = 6193,
            name = "Leonardo DiCaprio",
            alsoKnownAs = listOf("Leo", "Leonardo Wilhelm DiCaprio"),
            biography = "Leonardo Wilhelm DiCaprio is an American actor and film producer. Known for his work in biopics and period films, DiCaprio has received numerous accolades throughout his career, including an Academy Award, a British Academy Film Award, and three Golden Globe Awards. As of 2019, his films have grossed over $7.2 billion worldwide, and he has been placed eight times in annual rankings of the world's highest-paid actors.",
            birthday = "1974-11-11",
            deathday = null,
            placeOfBirth = "Los Angeles, California, USA",
            knownForDepartment = "Acting",
            gender = 2,
            popularity = 45.933,
            adult = false,
            imdbId = "nm0000138",
            homepage = null,
            profile = "https://image.tmdb.org/t/p/w500/wo2hJpn04vbtmh0B9utCFdsQhxM.jpg",
            knownFor = listOf("Inception", "Titanic", "The Revenant", "The Wolf of Wall Street"),
            movieCredits = Credits(
                cast = listOf(
                    CreditItem(27205, "Inception", null, "Dom Cobb", null, null, "2010-07-15", null, 2010, "https://image.tmdb.org/t/p/w500/9gk7adHYeDvHkCSEqAvQNLV5Uge.jpg", 8.8, 31.26, 0, "52fe44959251416c750ac9c3", null),
                    CreditItem(597, "Titanic", null, "Jack Dawson", null, null, "1997-11-18", null, 1997, "https://image.tmdb.org/t/p/w500/9xjZS2rlVxm8SFx8kPC3aIGCOYQ.jpg", 7.9, 42.933, 1, "52fe4232c3a36847f80149f5", null),
                    CreditItem(453405, "The Revenant", null, "Hugh Glass", null, null, "2015-12-25", null, 2015, "https://image.tmdb.org/t/p/w500/tSaBkriE7TpbjFoQUFXuikoz0dF.jpg", 8.0, 25.944, 0, "5571d9f19251415cfb0010dc", null)
                ),
                crew = emptyList()
            ),
            tvCredits = Credits(
                cast = listOf(
                    CreditItem(1402, null, "Growing Pains", "Luke Brower", null, null, null, "1985-09-24", 1991, "https://image.tmdb.org/t/p/w500/eKyeUFwjc0LhPSp129IHpXniJVR.jpg", 6.2, 4.956, null, "525333fb19c29579400008bb", 23)
                ),
                crew = emptyList()
            )
        ),
        3223 to PersonDetails(
            id = 3223,
            name = "Robert Downey Jr.",
            alsoKnownAs = listOf("RDJ", "Robert John Downey Jr."),
            biography = "Robert John Downey Jr. is an American actor and producer. His career has been characterized by critical and popular success in his youth, followed by a period of substance abuse and legal troubles, before a resurgence of commercial success later in his career.",
            birthday = "1965-04-04",
            deathday = null,
            placeOfBirth = "New York City, New York, USA",
            knownForDepartment = "Acting",
            gender = 2,
            popularity = 58.132,
            adult = false,
            imdbId = "nm0000375",
            homepage = null,
            profile = "https://image.tmdb.org/t/p/w500/5qHNjhtjMD4YWH3UP0rm4tKwxCL.jpg",
            knownFor = listOf("Iron Man", "The Avengers", "Sherlock Holmes", "Tropic Thunder"),
            movieCredits = Credits(
                cast = listOf(
                    CreditItem(1726, "Iron Man", null, "Tony Stark / Iron Man", null, null, "2008-04-30", null, 2008, "https://image.tmdb.org/t/p/w500/78lPtwv72eTNqFW9COBYI0dWDJa.jpg", 7.6, 78.021, 0, "52fe4302c3a36847f8019f9b", null),
                    CreditItem(24428, "The Avengers", null, "Tony Stark / Iron Man", null, null, "2012-04-25", null, 2012, "https://image.tmdb.org/t/p/w500/RYMX2wcKCBAr24UyPD7xwmjaTn.jpg", 7.7, 118.864, 0, "52fe4751c3a36847f8024f49", null)
                ),
                crew = listOf(
                    CreditItem(1726, "Iron Man", null, null, "Executive Producer", "Production", "2008-04-30", null, 2008, "https://image.tmdb.org/t/p/w500/78lPtwv72eTNqFW9COBYI0dWDJa.jpg", 7.6, 78.021, null, "52fe4302c3a36847f8019f9c", null)
                )
            ),
            tvCredits = Credits(
                cast = emptyList(),
                crew = emptyList()
            )
        )
    )

    // Mock streaming services
    private val mockStreamingServices = listOf(
        StreamingService(1, "Netflix", "https://image.tmdb.org/t/p/w500/netflix_icon.png", "https://www.netflix.com"),
        StreamingService(2, "Amazon Prime Video", "https://image.tmdb.org/t/p/w500/prime_icon.png", "https://www.primevideo.com"),
        StreamingService(3, "Disney+", "https://image.tmdb.org/t/p/w500/disney_icon.png", "https://www.disneyplus.com"),
        StreamingService(4, "HBO Max", "https://image.tmdb.org/t/p/w500/hbo_icon.png", "https://www.hbomax.com"),
        StreamingService(5, "Hulu", "https://image.tmdb.org/t/p/w500/hulu_icon.png", "https://www.hulu.com")
    )

    /**
     * Search for movies, TV shows, persons, and genres
     */
    suspend fun search(query: String): Result<SearchResponse> {
        if (useMockData) {
            // Find matching mock data based on query
            val matchingResult = mockSearchResults.entries.find { (key, _) ->
                query.lowercase().contains(key.lowercase()) || key.lowercase().contains(query.lowercase())
            }?.value

            return if (matchingResult != null) {
                Timber.d("Mock search successful for query: $query, found ${matchingResult.results.size} results")
                Result.success(matchingResult.copy(query = query))
            } else {
                // Return empty results for unmatched queries
                val emptyResponse = SearchResponse(
                    query = query,
                    type = "unified",
                    results = emptyList(),
                    meta = SearchMeta("req_empty", java.time.Instant.now().toString())
                )
                Timber.d("Mock search returned no results for query: $query")
                Result.success(emptyResponse)
            }
        }

        return try {
            val response = searchApiService.search(query)
            Timber.d("Search successful for query: $query, found ${response.results.size} results")
            Result.success(response)
        } catch (e: Exception) {
            Timber.e(e, "Error searching for: $query")
            Result.failure(e)
        }
    }
    
    /**
     * Get movie details
     */
    suspend fun getMovieDetails(movieId: Int): Result<MovieDetails> {
        if (useMockData) {
            val mockDetails = mockMovieDetails[movieId]
            return if (mockDetails != null) {
                Timber.d("Mock movie details retrieved for ID: $movieId")
                Result.success(mockDetails)
            } else {
                Timber.e("Mock movie details not found for ID: $movieId")
                Result.failure(Exception("Movie not found"))
            }
        }

        return try {
            val details = searchApiService.getMovieDetails(movieId)
            Timber.d("Movie details retrieved for ID: $movieId")
            Result.success(details)
        } catch (e: Exception) {
            Timber.e(e, "Error getting movie details for ID: $movieId")
            Result.failure(e)
        }
    }
    
    /**
     * Get TV show details
     */
    suspend fun getTVDetails(tvShowId: Int): Result<TVDetails> {
        if (useMockData) {
            val mockDetails = mockTVDetails[tvShowId]
            return if (mockDetails != null) {
                Timber.d("Mock TV details retrieved for ID: $tvShowId")
                Result.success(mockDetails)
            } else {
                Timber.e("Mock TV details not found for ID: $tvShowId")
                Result.failure(Exception("TV show not found"))
            }
        }

        return try {
            val details = searchApiService.getTVDetails(tvShowId)
            Timber.d("TV details retrieved for ID: $tvShowId")
            Result.success(details)
        } catch (e: Exception) {
            Timber.e(e, "Error getting TV details for ID: $tvShowId")
            Result.failure(e)
        }
    }
    
    /**
     * Get person details
     */
    suspend fun getPersonDetails(personId: Int): Result<PersonDetails> {
        if (useMockData) {
            val mockDetails = mockPersonDetails[personId]
            return if (mockDetails != null) {
                Timber.d("Mock person details retrieved for ID: $personId")
                Result.success(mockDetails)
            } else {
                Timber.e("Mock person details not found for ID: $personId")
                Result.failure(Exception("Person not found"))
            }
        }

        return try {
            val details = searchApiService.getPersonDetails(personId)
            Timber.d("Person details retrieved for ID: $personId")
            Result.success(details)
        } catch (e: Exception) {
            Timber.e(e, "Error getting person details for ID: $personId")
            Result.failure(e)
        }
    }
    
    /**
     * Get streaming services for content
     */
    suspend fun getStreamingServices(contentType: String, contentId: Int): Result<List<StreamingService>> {
        if (useMockData) {
            Timber.d("Mock streaming services retrieved for $contentType ID: $contentId")
            return Result.success(mockStreamingServices)
        }

        return try {
            val services = searchApiService.getStreamingServices(contentType, contentId)
            Timber.d("Streaming services retrieved for $contentType ID: $contentId")
            Result.success(services)
        } catch (e: Exception) {
            Timber.e(e, "Error getting streaming services for $contentType ID: $contentId")
            // Return mock data for demo purposes
            Result.success(mockStreamingServices)
        }
    }
    
    /**
     * Toggle watchlist status
     */
    suspend fun toggleWatchlist(contentId: Int, contentType: String, isCurrentlyInWatchlist: Boolean): Result<WatchlistResponse> {
        if (useMockData) {
            val action = if (isCurrentlyInWatchlist) "remove" else "add"
            val response = WatchlistResponse(
                success = true,
                message = if (isCurrentlyInWatchlist) "Removed from watchlist" else "Added to watchlist",
                isInWatchlist = !isCurrentlyInWatchlist
            )
            Timber.d("Mock watchlist toggled for $contentType ID: $contentId, action: $action")
            return Result.success(response)
        }

        return try {
            val action = if (isCurrentlyInWatchlist) "remove" else "add"
            val request = WatchlistToggleRequest(contentId, contentType, action)
            val response = searchApiService.toggleWatchlist(request)
            Timber.d("Watchlist toggled for $contentType ID: $contentId, action: $action")
            Result.success(response)
        } catch (e: Exception) {
            Timber.e(e, "Error toggling watchlist for $contentType ID: $contentId")
            // Return mock success response
            Result.success(WatchlistResponse(
                success = true,
                message = if (isCurrentlyInWatchlist) "Removed from watchlist" else "Added to watchlist",
                isInWatchlist = !isCurrentlyInWatchlist
            ))
        }
    }
    
    /**
     * Check if content is in watchlist
     */
    suspend fun isInWatchlist(contentType: String, contentId: Int): Result<Boolean> {
        if (useMockData) {
            // For mock data, randomly return true/false for demo purposes
            val isInWatchlist = (contentId % 3 == 0) // Simple logic: every 3rd item is in watchlist
            Timber.d("Mock watchlist status checked for $contentType ID: $contentId, result: $isInWatchlist")
            return Result.success(isInWatchlist)
        }

        return try {
            val response = searchApiService.isInWatchlist(contentType, contentId)
            Timber.d("Watchlist status checked for $contentType ID: $contentId")
            Result.success(response.isInWatchlist)
        } catch (e: Exception) {
            Timber.e(e, "Error checking watchlist status for $contentType ID: $contentId")
            // Return false as default
            Result.success(false)
        }
    }
}
