package com.thyview.services

import com.thyview.models.*
import retrofit2.http.*

/**
 * REST API interface for user-related operations
 */
interface UserApiService {
    
    /**
     * Create a new user
     */
    @POST("users")
    suspend fun createUser(@Body request: CreateUserRequest): User

    /**
     * Get current authenticated user
     */
    @GET("users/me")
    suspend fun getCurrentUser(): User

    /**
     * Get user by external ID
     */
    @GET("users/me")
    suspend fun getUserByExternalId(@Query("externalUserId") externalUserId: String): User

    /**
     * Update current user profile
     */
    @PUT("users/me")
    suspend fun updateUser(@Body request: UpdateUserRequest): User

    /**
     * Delete current user
     */
    @DELETE("users/me")
    suspend fun deleteUser(): DeleteUserResponse

    /**
     * Check username availability
     */
    @GET("users/check-username")
    suspend fun checkUsernameAvailability(@Query("username") username: String): UsernameAvailabilityResponse

    /**
     * Get presigned URL for profile image upload
     */
    @GET("users/profile-image/presign")
    suspend fun getProfileImagePresignedUrl(): ProfileImagePresignResponse

    /**
     * Get current user's profile image URL
     */
    @GET("users/profile-image-url")
    suspend fun getProfileImageUrl(): ProfileImageUrlResponse
}
