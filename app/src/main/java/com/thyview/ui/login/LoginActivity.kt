package com.thyview.ui.login

import android.content.Context
import android.content.DialogInterface
import android.content.Intent
import android.os.Bundle
import androidx.activity.ComponentActivity
import androidx.activity.compose.setContent
import androidx.activity.viewModels
import com.google.firebase.auth.ktx.auth
import com.google.firebase.ktx.Firebase
import com.thyview.R
import com.thyview.services.AuthService
import com.thyview.utils.AlertDialogView
import com.thyview.utils.ScreenRouter

/**
 * Activity that hosts the Compose-based login screen
 */
class LoginActivity : ComponentActivity() {

    private val viewModel: LoginViewModel by viewModels()

    companion object {
        const val IS_USER_REPORTED = "IS_USER_REPORTED"
        const val SHOULD_DELETE_USER = "SHOULD_DELETE_USER"
        const val REPORTED_USER_TITLE = "REPORTED_USER_TITLE"
        const val REPORTED_USER_MESSAGE = "REPORTED_USER_MESSAGE"
        const val IS_USER_AGE_RESTRICTED = "IS_USER_AGE_RESTRICTED"

        fun newIntent(context: Context): Intent {
            return Intent(context, LoginActivity::class.java)
        }

        fun newIntentWithClearBackStack(context: Context): Intent {
            val intent = Intent(context, LoginActivity::class.java)
            intent.addFlags(Intent.FLAG_ACTIVITY_NEW_TASK or Intent.FLAG_ACTIVITY_CLEAR_TASK)
            return intent
        }
    }

    override fun onCreate(savedInstanceState: Bundle?) {
        super.onCreate(savedInstanceState)
        
        // Check for reported users or other conditions from intent
        val shouldDelete = intent?.getBooleanExtra(SHOULD_DELETE_USER, false)
        val isUserReported = intent?.getBooleanExtra(IS_USER_REPORTED, false)
        val isUserAgeRestricted = intent?.getBooleanExtra(IS_USER_AGE_RESTRICTED, false)
        val reportedTitle = intent?.getStringExtra(REPORTED_USER_TITLE)
        val reportedMessage = intent?.getStringExtra(REPORTED_USER_MESSAGE)

        // Clear auth state if not deleting user
        if (shouldDelete != true) {
            AuthService.cleanup(this)
        }

        // Show appropriate dialogs based on intent flags
        when {
            isUserReported == true && !reportedTitle.isNullOrEmpty() && !reportedMessage.isNullOrEmpty() -> {
                startUserReportedFlow(shouldDelete, reportedTitle, reportedMessage)
                return
            }
        }

        // Set up the login UI
        setContent {
            LoginScreen(
                viewModel = viewModel,
                onNavigateToMain = { navigateToMainScreen() },
                onNavigateToOnboarding = { navigateToOnboardingScreen() }
            )
        }
    }

    override fun onStart() {
        super.onStart()
        
        // Check if user is already signed in and redirect if needed
        val currentUser = Firebase.auth.currentUser
        if (currentUser != null) {
            navigateToMainScreen()
        }
    }

    private fun navigateToMainScreen() {
        finish()
    }

    private fun navigateToOnboardingScreen() {
        // For now, just finish the activity since onboarding will be handled
        // by the main navigation flow in the app
        finish()
    }

    private fun startUserReportedFlow(shouldDelete: Boolean?, title: String, message: String) {
        AlertDialogView.showAlertDialog(
            context = this,
            title = title,
            message = message,
            buttonPositiveText = getString(R.string.okay),
            buttonNegativeText = null
        ) { dialog, which ->
            if (which == DialogInterface.BUTTON_POSITIVE) {
                if (shouldDelete == true) {
                    // Delete user account
                    val user = Firebase.auth.currentUser
                    user?.delete()?.addOnCompleteListener { task ->
                        if (task.isSuccessful) {
                            AuthService.cleanup(this)
                        }
                        dialog.dismiss()
                        setContent {
                            LoginScreen(
                                viewModel = viewModel,
                                onNavigateToMain = { navigateToMainScreen() },
                                onNavigateToOnboarding = { navigateToOnboardingScreen() }
                            )
                        }
                    }
                } else {
                    dialog.dismiss()
                    setContent {
                        LoginScreen(
                            viewModel = viewModel,
                            onNavigateToMain = { navigateToMainScreen() },
                            onNavigateToOnboarding = { navigateToOnboardingScreen() }
                        )
                    }
                }
            }
        }
    }
}
