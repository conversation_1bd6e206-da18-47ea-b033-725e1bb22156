package com.thyview.ui.login

import android.content.Context
import android.content.Intent
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.facebook.AccessToken
import com.google.android.gms.auth.api.identity.Identity
import com.google.android.gms.auth.api.identity.SignInClient
import com.google.android.gms.common.api.ApiException
import com.google.android.gms.common.api.CommonStatusCodes
import com.google.firebase.auth.AuthCredential
import com.google.firebase.auth.FacebookAuthProvider
import com.google.firebase.auth.FirebaseAuth
import com.google.firebase.auth.GoogleAuthProvider
import com.google.firebase.auth.ktx.auth
import com.google.firebase.ktx.Firebase
import com.thyview.analytics.ANALYTICS_EVENT_SIGN_IN
import com.thyview.analytics.ANALYTICS_EVENT_SIGN_IN_FAILURE
import com.thyview.analytics.ERROR_REASON
import com.thyview.analytics.FirebaseAnalyticsTrackingService
import com.thyview.analytics.MixPanelAnalyticsTrackingService
import com.thyview.models.CreateUserRequest
import com.thyview.models.UpdateUserRequest
import com.thyview.models.UserObject
import com.thyview.repositories.UserRepository
import com.thyview.services.AuthService
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.Dispatchers
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import kotlinx.coroutines.tasks.await
import kotlinx.coroutines.withContext
import timber.log.Timber
import java.io.File
import java.io.FileOutputStream
import java.net.URL
import java.util.Locale
import javax.inject.Inject

/**
 * ViewModel for handling login business logic
 */
@HiltViewModel
class LoginViewModel @Inject constructor(
    private val userRepository: UserRepository
) : ViewModel() {
    private val auth: FirebaseAuth = Firebase.auth

    private val _shouldNavigateToOnboarding = MutableStateFlow(false)
    val shouldNavigateToOnboarding: StateFlow<Boolean> = _shouldNavigateToOnboarding.asStateFlow()

    private val _shouldNavigateToHome = MutableStateFlow(false)
    val shouldNavigateToHome: StateFlow<Boolean> = _shouldNavigateToHome.asStateFlow()
    
    /**
     * Handles the Facebook access token and performs authentication with Firebase
     */
    fun handleFacebookAccessToken(token: AccessToken) {
        val credential = FacebookAuthProvider.getCredential(token.token)
        firebaseAuthWithCredential(credential, SIGN_IN_FACEBOOK)
    }
    
    /**
     * Handles the Google sign-in result
     */
    fun handleGoogleSignInResult(
        context: Context,
        data: Intent?,
        onError: () -> Unit
    ) {
        try {
            val credential = getGoogleCredentialFromIntent(context, data)
            if (credential != null) {
                firebaseAuthWithCredential(credential, SIGN_IN_GOOGLE)
            } else {
                Timber.e("Google Sign In failed: credential is null")
                onError()
            }
        } catch (e: Exception) {
            Timber.e(e, "Google Sign In failed")
            onError()
        }
    }
    
    /**
     * Get Google credential from sign-in intent
     */
    private fun getGoogleCredentialFromIntent(context: Context, data: Intent?): AuthCredential? {
        return try {
            val signInClient: SignInClient = Identity.getSignInClient(context)
            val credential = signInClient.getSignInCredentialFromIntent(data)
            val idToken = credential.googleIdToken
            
            if (idToken != null) {
                GoogleAuthProvider.getCredential(idToken, null)
            } else {
                Timber.e("Google Sign In failed: No ID token!")
                null
            }
        } catch (e: ApiException) {
            when (e.statusCode) {
                CommonStatusCodes.CANCELED -> {
                    Timber.d("Google Sign In canceled")
                }
                CommonStatusCodes.NETWORK_ERROR -> {
                    Timber.e("Google Sign In network error")
                }
                else -> {
                    Timber.e(e, "Google Sign In API exception")
                }
            }
            null
        } catch (e: Exception) {
            Timber.e(e, "Google Sign In exception")
            null
        }
    }
    
    /**
     * Authenticate with Firebase using the provided credential
     */
    private fun firebaseAuthWithCredential(
        credential: AuthCredential,
        signInMethod: String
    ) {
        viewModelScope.launch {
            try {
                val authResult = auth.signInWithCredential(credential).await()
                val user = authResult.user

                if (user != null) {
                    handleSuccessfulLogin(user.uid, signInMethod)

                    val firebaseIdToken = user.getIdToken(false).await().token
                    Timber.d("Firebase auth successful Id token: $firebaseIdToken")

                    // Check if user exists in our backend
                    checkUserExistsAndNavigate()
                } else {
                    Timber.e("Firebase auth failed: User is null")
                    logAuthFailure(signInMethod, "User is null")
                }
            } catch (e: Exception) {
                Timber.e(e, "Firebase auth failed")
                logAuthFailure(signInMethod, e.message ?: "Unknown error")
            }
        }
    }

    /**
     * Check if user exists in backend and navigate accordingly
     */
    private fun checkUserExistsAndNavigate() {
        viewModelScope.launch {
            try {
                Timber.d("Checking if user exists in backend...")
                val result = userRepository.getCurrentUser()
                result.fold(
                    onSuccess = { user ->
                        // User exists, check if we need to update profile image
                        checkAndUpdateProfileImage()
                        _shouldNavigateToHome.value = true
                        Timber.d("Existing user found: ${user.username}, navigating to home")
                    },
                    onFailure = { exception ->
                        // Check if it's a 404 (user not found) or other error
                        val errorMessage = exception.message?.lowercase() ?: ""
                        if (errorMessage.contains("404") || errorMessage.contains("not found")) {
                            // User doesn't exist, create user immediately with Firebase data
                            createUserWithFirebaseData()
                        } else {
                            // Other error, log it but still try creating user
                            Timber.w(exception, "Error checking user existence (${exception.message}), attempting to create user")
                            createUserWithFirebaseData()
                        }
                    }
                )
            } catch (e: Exception) {
                // On error, assume new user and try to create
                Timber.e(e, "Exception checking user existence, attempting to create user")
                createUserWithFirebaseData()
            }
        }
    }

    /**
     * Create user immediately after Firebase authentication with available data
     */
    private fun createUserWithFirebaseData() {
        viewModelScope.launch {
            try {
                val email = AuthService.getCurrentUserEmail()
                val name = AuthService.getCurrentUserDisplayName()
                val profileImageUrl = AuthService.getCurrentUserPhotoUrl()
                val region = Locale.getDefault().country.ifEmpty { "US" }

                if (email != null && name != null) {
                    // First, upload the profile image from Firebase Auth if available
                    val finalProfileImageUrl = if (!profileImageUrl.isNullOrBlank()) {
                        uploadFirebaseProfileImageAndGetUrl(profileImageUrl) ?: profileImageUrl
                    } else {
                        profileImageUrl
                    }

                    val createUserRequest = CreateUserRequest(
                        email = email,
                        name = name,
                        profileImageUrl = finalProfileImageUrl,
                        region = region
                    )

                    val createResult = userRepository.createUser(createUserRequest)
                    createResult.fold(
                        onSuccess = { user ->
                            Timber.d("User created successfully: ${user.id}, navigating to onboarding")
                            _shouldNavigateToOnboarding.value = true
                        },
                        onFailure = { exception ->
                            Timber.e(exception, "Failed to create user, navigating to onboarding anyway")
                            _shouldNavigateToOnboarding.value = true
                        }
                    )
                } else {
                    Timber.w("Missing required user information from Firebase Auth")
                    _shouldNavigateToOnboarding.value = true
                }
            } catch (e: Exception) {
                Timber.e(e, "Exception creating user, navigating to onboarding")
                _shouldNavigateToOnboarding.value = true
            }
        }
    }
    
    /**
     * Handle successful login by storing user data and logging analytics
     */
    private fun handleSuccessfulLogin(userId: String, signInMethod: String) {
        // Set user ID for tracking and analytics
        UserObject.userUID = userId
        
        // Log success events
        FirebaseAnalyticsTrackingService.logEvent(
            context = null,
            name = ANALYTICS_EVENT_SIGN_IN,
            bundle = null
        )
        MixPanelAnalyticsTrackingService.logEvent(
            context = null,
            name = ANALYTICS_EVENT_SIGN_IN,
            map = hashMapOf(MP_LOGIN_METHOD to signInMethod)
        )
    }
    
    /**
     * Log authentication failure to analytics
     */
    private fun logAuthFailure(signInMethod: String, errorReason: String) {
        FirebaseAnalyticsTrackingService.logEvent(
            context = null,
            name = ANALYTICS_EVENT_SIGN_IN_FAILURE,
            bundle = null
        )
        MixPanelAnalyticsTrackingService.logEvent(
            context = null,
            name = ANALYTICS_EVENT_SIGN_IN_FAILURE, 
            map = hashMapOf(
                MP_LOGIN_METHOD to signInMethod,
                ERROR_REASON to errorReason
            )
        )
    }

    /**
     * Check and update profile image if needed
     */
    private fun checkAndUpdateProfileImage() {
        viewModelScope.launch {
            try {
                // Check if user has no profile image or has Firebase image
                val firebasePhotoUrl = AuthService.getCurrentUserPhotoUrl()

                if (!firebasePhotoUrl.isNullOrBlank()) {
                    Timber.d("User has no profile image, uploading Firebase image")
                    uploadFirebaseProfileImage(firebasePhotoUrl)
                }
            } catch (e: Exception) {
                Timber.e(e, "Error checking/updating profile image")
            }
        }
    }

    /**
     * Upload Firebase profile image to our S3 bucket
     */
    private suspend fun uploadFirebaseProfileImage(firebaseImageUrl: String) {
        try {
            Timber.d("Uploading Firebase profile image to S3: $firebaseImageUrl")

            // Step 1: Download image from Firebase URL
            val imageFile = downloadImageFromUrl(firebaseImageUrl)
            if (imageFile == null) {
                Timber.w("Failed to download Firebase profile image")
                return
            }

            // Step 2: Get presigned URL
            val presignResult = userRepository.getProfileImagePresignedUrl()
            presignResult.fold(
                onSuccess = { presignResponse ->
                    // Step 3: Upload to S3
                    val uploadResult = userRepository.uploadImageToS3(presignResponse.url, imageFile)
                    uploadResult.fold(
                        onSuccess = {
                            // Step 4: Update user profile
                            val s3ImageUrl = extractImageUrlFromKey(presignResponse.key)
                            val updateRequest = UpdateUserRequest(profileImageUrl = s3ImageUrl)
                            userRepository.updateUser(updateRequest)
                            Timber.d("Profile image updated successfully: $s3ImageUrl")
                        },
                        onFailure = { exception ->
                            Timber.e(exception, "Failed to upload Firebase image to S3")
                        }
                    )
                },
                onFailure = { exception ->
                    Timber.e(exception, "Failed to get presigned URL for Firebase image")
                }
            )

            // Clean up
            imageFile.delete()
        } catch (e: Exception) {
            Timber.e(e, "Error uploading Firebase profile image")
        }
    }

    /**
     * Upload Firebase profile image to our S3 bucket and return the URL
     */
    private suspend fun uploadFirebaseProfileImageAndGetUrl(firebaseImageUrl: String): String? {
        return try {
            Timber.d("Uploading Firebase profile image to S3: $firebaseImageUrl")

            // Step 1: Download image from Firebase URL
            val imageFile = downloadImageFromUrl(firebaseImageUrl)
            if (imageFile == null) {
                Timber.w("Failed to download Firebase profile image")
                return null
            }

            // Step 2: Get presigned URL
            val presignResult = userRepository.getProfileImagePresignedUrl()
            presignResult.fold(
                onSuccess = { presignResponse ->
                    // Step 3: Upload to S3
                    val uploadResult = userRepository.uploadImageToS3(presignResponse.url, imageFile)
                    uploadResult.fold(
                        onSuccess = {
                            Timber.d("Firebase profile image uploaded to S3 successfully")
                            imageFile.delete() // Clean up
                            // Return the S3 URL
                            extractImageUrlFromKey(presignResponse.key)
                        },
                        onFailure = { exception ->
                            Timber.e(exception, "Failed to upload Firebase image to S3")
                            imageFile.delete() // Clean up
                            null
                        }
                    )
                },
                onFailure = { exception ->
                    Timber.e(exception, "Failed to get presigned URL for Firebase image")
                    imageFile.delete() // Clean up
                    null
                }
            )
        } catch (e: Exception) {
            Timber.e(e, "Error uploading Firebase profile image")
            null
        }
    }

    /**
     * Download image from URL to temporary file
     */
    private suspend fun downloadImageFromUrl(imageUrl: String): File? {
        return withContext(Dispatchers.IO) {
            try {
                val url = URL(imageUrl)
                val connection = url.openConnection()
                val inputStream = connection.getInputStream()

                val tempFile = File.createTempFile("firebase_profile_", ".jpg")
                FileOutputStream(tempFile).use { output ->
                    inputStream.copyTo(output)
                }

                tempFile
            } catch (e: Exception) {
                Timber.e(e, "Failed to download image from URL: $imageUrl")
                null
            }
        }
    }

    /**
     * Extract the public image URL from S3 key
     */
    private fun extractImageUrlFromKey(key: String): String {
        return "https://thyview-profile-pictures.s3.us-east-1.amazonaws.com/$key"
    }

    /**
     * Reset navigation flags
     */
    fun resetNavigationFlags() {
        _shouldNavigateToOnboarding.value = false
        _shouldNavigateToHome.value = false
    }



    companion object {
        private const val SIGN_IN_GOOGLE = "google"
        private const val SIGN_IN_FACEBOOK = "facebook"
        private const val MP_LOGIN_METHOD = "login_method"
    }
}