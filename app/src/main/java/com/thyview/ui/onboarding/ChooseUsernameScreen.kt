package com.thyview.ui.onboarding

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalSoftwareKeyboardController
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import androidx.navigation.compose.rememberNavController
import com.thyview.R
import com.thyview.ui.ImdbBlack
import com.thyview.ui.ImdbWhite
import com.thyview.ui.ImdbYellow
import com.thyview.ui.NavRoutes
import com.thyview.ui.ThyViewTheme
import com.thyview.ui.profile.UserViewModel

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun ChooseUsernameScreen(
    navController: NavController,
    userViewModel: UserViewModel = hiltViewModel(),
    onboardingViewModel: OnboardingViewModel = hiltViewModel(navController.getBackStackEntry(NavRoutes.CHOOSE_USERNAME))
) {
    var username by remember { mutableStateOf("") }
    var isValidating by remember { mutableStateOf(false) }
    val keyboardController = LocalSoftwareKeyboardController.current
    
    val usernameAvailable by userViewModel.usernameAvailable.collectAsStateWithLifecycle()
    val error by userViewModel.error.collectAsStateWithLifecycle()
    val isLoading by userViewModel.isLoading.collectAsStateWithLifecycle()

    // Clear previous state when screen loads
    LaunchedEffect(Unit) {
        userViewModel.clearUsernameAvailability()
        userViewModel.clearError()
    }

    ThyViewTheme {
        Scaffold(
            topBar = {
                TopAppBar(
                    title = { 
                        Text(
                            text = "Choose Username",
                            color = ImdbYellow,
                            modifier = Modifier.fillMaxWidth(),
                            textAlign = TextAlign.Center
                        ) 
                    },
                    navigationIcon = {
                        IconButton(onClick = { navController.popBackStack() }) {
                            Icon(
                                imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                                contentDescription = "Back",
                                tint = ImdbYellow
                            )
                        }
                    },
                    colors = TopAppBarDefaults.topAppBarColors(
                        containerColor = ImdbBlack,
                        titleContentColor = ImdbYellow
                    )
                )
            },
            containerColor = ImdbBlack
        ) { paddingValues ->
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues)
                    .padding(24.dp),
                horizontalAlignment = Alignment.CenterHorizontally
            ) {
                Spacer(modifier = Modifier.height(32.dp))

                // Instructions
                Text(
                    text = "Choose a unique username that represents you",
                    color = ImdbWhite,
                    style = MaterialTheme.typography.bodyLarge,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.padding(horizontal = 16.dp)
                )

                Spacer(modifier = Modifier.height(32.dp))

                // Username input field
                OutlinedTextField(
                    value = username,
                    onValueChange = { 
                        username = it
                        userViewModel.clearUsernameAvailability()
                        userViewModel.clearError()
                    },
                    label = { Text("Username", color = ImdbWhite.copy(alpha = 0.7f)) },
                    placeholder = { Text("Enter your username", color = ImdbWhite.copy(alpha = 0.5f)) },
                    singleLine = true,
                    keyboardOptions = KeyboardOptions(imeAction = ImeAction.Done),
                    keyboardActions = KeyboardActions(
                        onDone = {
                            keyboardController?.hide()
                            if (username.isNotBlank()) {
                                isValidating = true
                                userViewModel.checkUsernameAvailability(username.trim())
                            }
                        }
                    ),
                    colors = OutlinedTextFieldDefaults.colors(
                        focusedTextColor = ImdbWhite,
                        unfocusedTextColor = ImdbWhite,
                        focusedBorderColor = ImdbYellow,
                        unfocusedBorderColor = ImdbWhite.copy(alpha = 0.5f),
                        cursorColor = ImdbYellow
                    ),
                    modifier = Modifier.fillMaxWidth()
                )

                Spacer(modifier = Modifier.height(16.dp))

                // Validation status
                when {
                    error != null -> {
                        Text(
                            text = error!!,
                            color = MaterialTheme.colorScheme.error,
                            style = MaterialTheme.typography.bodyMedium,
                            textAlign = TextAlign.Center
                        )
                    }
                    usernameAvailable == true -> {
                        Text(
                            text = "✓ Username is available!",
                            color = ImdbYellow,
                            style = MaterialTheme.typography.bodyMedium,
                            fontWeight = FontWeight.Medium,
                            textAlign = TextAlign.Center
                        )
                    }
                    usernameAvailable == false -> {
                        Text(
                            text = "✗ Username is already taken",
                            color = MaterialTheme.colorScheme.error,
                            style = MaterialTheme.typography.bodyMedium,
                            textAlign = TextAlign.Center
                        )
                    }
                }

                Spacer(modifier = Modifier.weight(1f))

                // Continue button
                Button(
                    onClick = {
                        if (username.isNotBlank()) {
                            if (usernameAvailable == true) {
                                onboardingViewModel.setUsername(username.trim())
                                navController.navigate(NavRoutes.SELECT_LANGUAGES)
                            } else {
                                isValidating = true
                                userViewModel.checkUsernameAvailability(username.trim())
                            }
                        }
                    },
                    enabled = username.isNotBlank() && !isLoading,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = ImdbYellow,
                        contentColor = ImdbBlack,
                        disabledContainerColor = ImdbYellow.copy(alpha = 0.5f),
                        disabledContentColor = ImdbBlack.copy(alpha = 0.5f)
                    ),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    if (isLoading) {
                        CircularProgressIndicator(
                            color = ImdbBlack,
                            modifier = Modifier.size(20.dp)
                        )
                    } else {
                        Text(
                            text = if (usernameAvailable == true) "Continue" else "Check Availability",
                            fontWeight = FontWeight.Medium
                        )
                    }
                }

                Spacer(modifier = Modifier.height(16.dp))
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun ChooseUsernameScreenPreview() {
    val mockNavController = rememberNavController()
    ThyViewTheme {
        ChooseUsernameScreen(navController = mockNavController)
    }
}
