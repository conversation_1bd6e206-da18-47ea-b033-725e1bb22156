package com.thyview.ui.onboarding

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.thyview.models.UpdateUserRequest
import com.thyview.repositories.UserRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

/**
 * ViewModel for handling onboarding flow
 */
@HiltViewModel
class OnboardingViewModel @Inject constructor(
    private val userRepository: UserRepository
) : ViewModel() {

    private val _selectedUsername = MutableStateFlow("")
    val selectedUsername: StateFlow<String> = _selectedUsername.asStateFlow()

    private val _selectedLanguages = MutableStateFlow(setOf<String>())
    val selectedLanguages: StateFlow<Set<String>> = _selectedLanguages.asStateFlow()

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()

    private val _onboardingComplete = MutableStateFlow(false)
    val onboardingComplete: StateFlow<Boolean> = _onboardingComplete.asStateFlow()

    /**
     * Set selected username
     */
    fun setUsername(username: String) {
        _selectedUsername.value = username
        Timber.d("Username set in OnboardingViewModel: '$username'")
    }

    /**
     * Set selected languages
     */
    fun setLanguages(languages: Set<String>) {
        _selectedLanguages.value = languages
        Timber.d("Languages set in OnboardingViewModel: $languages")
    }

    /**
     * Complete onboarding by updating user with collected onboarding data
     */
    fun completeOnboarding() {
        viewModelScope.launch {
            _isLoading.value = true
            _error.value = null

            try {
                // Update user with username and languages if they were selected
                Timber.d("Completing onboarding - Username: '${_selectedUsername.value}', Languages: ${_selectedLanguages.value}")
                if (_selectedUsername.value.isNotEmpty() || _selectedLanguages.value.isNotEmpty()) {
                    val updateRequest = UpdateUserRequest(
                        username = _selectedUsername.value.takeIf { it.isNotEmpty() },
                        preferredLanguages = _selectedLanguages.value.toList().takeIf { it.isNotEmpty() }
                    )
                    Timber.d("Creating update request with username: '${updateRequest.username}', languages: ${updateRequest.preferredLanguages}")

                    val updateResult = userRepository.updateUser(updateRequest)
                    updateResult.fold(
                        onSuccess = { updatedUser ->
                            // Add a small delay to ensure the API has processed the update
                            kotlinx.coroutines.delay(500)
                            _onboardingComplete.value = true
                            Timber.d("Onboarding completed successfully for user: ${updatedUser.username}")
                        },
                        onFailure = { exception ->
                            _error.value = "Failed to complete profile setup: ${exception.message}"
                            Timber.e(exception, "Failed to update user during onboarding")
                        }
                    )
                } else {
                    // No updates needed, just mark onboarding as complete
                    _onboardingComplete.value = true
                    Timber.d("Onboarding completed successfully with no additional updates")
                }
            } catch (e: Exception) {
                _error.value = "Unexpected error during onboarding: ${e.message}"
                Timber.e(e, "Unexpected error during onboarding")
            } finally {
                _isLoading.value = false
            }
        }
    }





    /**
     * Reset onboarding state
     */
    fun resetOnboarding() {
        _selectedUsername.value = ""
        _selectedLanguages.value = setOf()
        _isLoading.value = false
        _error.value = null
        _onboardingComplete.value = false
    }
}
