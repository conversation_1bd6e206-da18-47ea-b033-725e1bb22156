package com.thyview.ui.profile

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.selection.toggleable
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.style.TextAlign
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.lifecycle.compose.collectAsStateWithLifecycle
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import com.thyview.R
import com.thyview.models.SupportedLanguages
import com.thyview.models.UpdateUserRequest
import com.thyview.ui.ImdbBlack
import com.thyview.ui.ImdbDarkGray
import com.thyview.ui.ImdbWhite
import com.thyview.ui.ImdbYellow
import com.thyview.ui.ThyViewTheme

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun LanguageSelectionScreen(
    navController: NavController,
    userViewModel: UserViewModel = hiltViewModel()
) {
    var selectedLanguages by remember { mutableStateOf(setOf<String>()) }
    
    val currentUser by userViewModel.currentUser.collectAsStateWithLifecycle()
    val isLoading by userViewModel.isLoading.collectAsStateWithLifecycle()
    val error by userViewModel.error.collectAsStateWithLifecycle()

    // Initialize with current user's preferred languages
    LaunchedEffect(currentUser) {
        currentUser?.let { user ->
            selectedLanguages = user.preferredLanguages.toSet()
        }
    }

    // Load user data when screen opens
    LaunchedEffect(Unit) {
        if (currentUser == null) {
            userViewModel.getCurrentUser()
        }
    }

    ThyViewTheme {
        Scaffold(
            topBar = {
                TopAppBar(
                    title = { 
                        Text(
                            text = "Preferred Languages",
                            color = ImdbYellow,
                            modifier = Modifier.fillMaxWidth(),
                        )
                    },
                    navigationIcon = {
                        IconButton(onClick = { navController.popBackStack() }) {
                            Icon(
                                imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                                contentDescription = "Back",
                                tint = ImdbYellow
                            )
                        }
                    },
                    colors = TopAppBarDefaults.topAppBarColors(
                        containerColor = ImdbBlack,
                        titleContentColor = ImdbYellow
                    ),
                    windowInsets = WindowInsets(0.dp)
                )
            },
            containerColor = ImdbBlack
        ) { paddingValues ->
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(paddingValues)
                    .padding(24.dp)
            ) {
                // Instructions
                Text(
                    text = "Select your preferred languages for content recommendations",
                    color = ImdbWhite,
                    style = MaterialTheme.typography.bodyLarge,
                    textAlign = TextAlign.Center,
                    modifier = Modifier.padding(horizontal = 16.dp)
                )

                Spacer(modifier = Modifier.height(24.dp))

                // Selected count
                Text(
                    text = "${selectedLanguages.size} language(s) selected",
                    color = ImdbYellow,
                    style = MaterialTheme.typography.bodyMedium,
                    fontWeight = FontWeight.Medium
                )

                Spacer(modifier = Modifier.height(16.dp))

                // Error message
                error?.let { errorMessage ->
                    Text(
                        text = errorMessage,
                        color = MaterialTheme.colorScheme.error,
                        style = MaterialTheme.typography.bodyMedium,
                        textAlign = TextAlign.Center,
                        modifier = Modifier.fillMaxWidth()
                    )
                    Spacer(modifier = Modifier.height(16.dp))
                }

                // Languages list
                LazyColumn(
                    modifier = Modifier.weight(1f),
                    verticalArrangement = Arrangement.spacedBy(8.dp)
                ) {
                    items(SupportedLanguages.languages) { language ->
                        LanguageItem(
                            language = language,
                            isSelected = selectedLanguages.contains(language),
                            onToggle = { isSelected ->
                                selectedLanguages = if (isSelected) {
                                    selectedLanguages + language
                                } else {
                                    selectedLanguages - language
                                }
                            }
                        )
                    }
                }

                Spacer(modifier = Modifier.height(24.dp))

                // Save button
                Button(
                    onClick = {
                        val updateRequest = UpdateUserRequest(
                            preferredLanguages = selectedLanguages.toList()
                        )
                        userViewModel.updateUser(updateRequest)
                        navController.popBackStack()
                    },
                    enabled = selectedLanguages.isNotEmpty() && !isLoading,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = ImdbYellow,
                        contentColor = ImdbBlack,
                        disabledContainerColor = ImdbYellow.copy(alpha = 0.5f),
                        disabledContentColor = ImdbBlack.copy(alpha = 0.5f)
                    ),
                    modifier = Modifier.fillMaxWidth()
                ) {
                    if (isLoading) {
                        CircularProgressIndicator(
                            color = ImdbBlack,
                            modifier = Modifier.size(20.dp)
                        )
                    } else {
                        Text(
                            text = "Save Languages",
                            fontWeight = FontWeight.Medium
                        )
                    }
                }

                Spacer(modifier = Modifier.height(16.dp))
            }
        }
    }
}

@Composable
private fun LanguageItem(
    language: String,
    isSelected: Boolean,
    onToggle: (Boolean) -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .toggleable(
                value = isSelected,
                onValueChange = onToggle
            ),
        colors = CardDefaults.cardColors(
            containerColor = if (isSelected) ImdbYellow.copy(alpha = 0.2f) else ImdbDarkGray
        )
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Text(
                text = language,
                color = if (isSelected) ImdbYellow else ImdbWhite,
                style = MaterialTheme.typography.bodyLarge,
                fontWeight = if (isSelected) FontWeight.Medium else FontWeight.Normal,
                modifier = Modifier.weight(1f)
            )
            
            Checkbox(
                checked = isSelected,
                onCheckedChange = onToggle,
                colors = CheckboxDefaults.colors(
                    checkedColor = ImdbYellow,
                    uncheckedColor = ImdbWhite.copy(alpha = 0.6f),
                    checkmarkColor = ImdbBlack
                )
            )
        }
    }
}

@Preview(showBackground = true)
@Composable
fun LanguageSelectionScreenPreview() {
    val mockNavController = rememberNavController()
    ThyViewTheme {
        LanguageSelectionScreen(navController = mockNavController)
    }
}
