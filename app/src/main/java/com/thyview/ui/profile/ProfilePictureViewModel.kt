package com.thyview.ui.profile

import android.content.Context
import android.net.Uri
import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.thyview.models.UpdateUserRequest
import com.thyview.repositories.UserRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import timber.log.Timber
import java.io.File
import java.io.FileOutputStream
import javax.inject.Inject

/**
 * ViewModel for handling profile picture upload
 */
@HiltViewModel
class ProfilePictureViewModel @Inject constructor(
    private val userRepository: UserRepository
) : ViewModel() {

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()

    private val _uploadSuccess = MutableStateFlow(false)
    val uploadSuccess: StateFlow<Boolean> = _uploadSuccess.asStateFlow()

    private val _profileImageUpdated = MutableStateFlow(false)
    val profileImageUpdated: StateFlow<Boolean> = _profileImageUpdated.asStateFlow()

    /**
     * Upload profile picture
     */
    fun uploadProfilePicture(context: Context, imageUri: Uri) {
        viewModelScope.launch {
            _isLoading.value = true
            _error.value = null
            _uploadSuccess.value = false

            try {
                // Step 1: Convert URI to File
                val imageFile = uriToFile(context, imageUri)
                if (imageFile == null) {
                    _error.value = "Failed to process selected image"
                    return@launch
                }

                // Step 2: Get presigned URL
                Timber.d("Getting presigned URL for profile image upload")
                val presignResult = userRepository.getProfileImagePresignedUrl()
                presignResult.fold(
                    onSuccess = { presignResponse ->
                        // Step 3: Upload to S3
                        Timber.d("Uploading image to S3")
                        val uploadResult = userRepository.uploadImageToS3(presignResponse.url, imageFile)
                        uploadResult.fold(
                            onSuccess = {
                                // Step 4: Get the updated profile image URL
                                getProfileImageUrl()
                            },
                            onFailure = { exception ->
                                _error.value = "Failed to upload image: ${exception.message}"
                                Timber.e(exception, "Failed to upload image to S3")
                            }
                        )
                    },
                    onFailure = { exception ->
                        _error.value = "Failed to get upload URL: ${exception.message}"
                        Timber.e(exception, "Failed to get presigned URL")
                    }
                )

                // Clean up temporary file
                imageFile.delete()

            } catch (e: Exception) {
                _error.value = "Unexpected error: ${e.message}"
                Timber.e(e, "Unexpected error during profile picture upload")
            } finally {
                _isLoading.value = false
            }
        }
    }

    /**
     * Get the updated profile image URL after upload
     */
    private suspend fun getProfileImageUrl() {
        try {
            Timber.d("Getting updated profile image URL")
            val result = userRepository.getProfileImageUrl()
            result.fold(
                onSuccess = { imageUrl ->
                    _uploadSuccess.value = true
                    _profileImageUpdated.value = true
                    Timber.d("Profile image URL retrieved successfully: $imageUrl")
                },
                onFailure = { exception ->
                    _error.value = "Failed to get profile image URL: ${exception.message}"
                    Timber.e(exception, "Failed to get profile image URL")
                }
            )
        } catch (e: Exception) {
            _error.value = "Failed to get profile image URL: ${e.message}"
            Timber.e(e, "Error getting profile image URL")
        }
    }

    /**
     * Convert URI to File
     */
    private fun uriToFile(context: Context, uri: Uri): File? {
        return try {
            val inputStream = context.contentResolver.openInputStream(uri)
            val tempFile = File(context.cacheDir, "temp_profile_image.jpg")
            
            inputStream?.use { input ->
                FileOutputStream(tempFile).use { output ->
                    input.copyTo(output)
                }
            }
            
            tempFile
        } catch (e: Exception) {
            Timber.e(e, "Failed to convert URI to file")
            null
        }
    }



    /**
     * Clear error state
     */
    fun clearError() {
        _error.value = null
    }

    /**
     * Reset upload state
     */
    fun resetUploadState() {
        _uploadSuccess.value = false
        _error.value = null
        _profileImageUpdated.value = false
    }

    /**
     * Reset profile image updated state
     */
    fun resetProfileImageUpdated() {
        _profileImageUpdated.value = false
    }
}
