package com.thyview.ui.profile

import android.content.Context
import android.content.Intent
import android.net.Uri
import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.automirrored.filled.ArrowBack
import androidx.compose.material.icons.automirrored.filled.KeyboardArrowRight
import androidx.compose.material.icons.automirrored.filled.ExitToApp
import androidx.compose.material.icons.filled.Delete
import androidx.compose.material.icons.outlined.Policy
import androidx.compose.material.icons.outlined.Description
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.platform.LocalContext
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import com.google.firebase.auth.ktx.auth
import com.google.firebase.ktx.Firebase
import com.thyview.R
import com.thyview.services.AuthService
import com.thyview.ui.ImdbBlack
import com.thyview.ui.ImdbDarkGray
import com.thyview.ui.ImdbWhite
import com.thyview.ui.ImdbYellow
import com.thyview.ui.NavRoutes
import com.thyview.ui.ThyViewTheme

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SettingsScreen(
    navController: NavController,
    isUserAuthenticated: Boolean = true
) {
    val context = LocalContext.current
    var showDeleteDialog by remember { mutableStateOf(false) }

    ThyViewTheme {
        Scaffold(
            topBar = {
                TopAppBar(
                    title = { 
                        Text(
                            text = stringResource(R.string.settings),
                            color = ImdbYellow
                        ) 
                    },
                    navigationIcon = {
                        IconButton(onClick = { navController.popBackStack() }) {
                            Icon(
                                imageVector = Icons.AutoMirrored.Filled.ArrowBack,
                                contentDescription = "Back",
                                tint = ImdbYellow
                            )
                        }
                    },
                    colors = TopAppBarDefaults.topAppBarColors(
                        containerColor = ImdbBlack,
                        titleContentColor = ImdbYellow
                    )
                )
            },
            containerColor = ImdbBlack
        ) { innerPadding ->
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(innerPadding)
                    .background(ImdbBlack)
            ) {
                // Privacy Policy
                SettingsItem(
                    icon = Icons.Outlined.Policy,
                    title = stringResource(R.string.privacy_policy_title),
                    onClick = {
                        openExternalLink(context, "https://www.LoveBeats.co/privacy-policy")
                    }
                )

                Spacer(modifier = Modifier.height(8.dp))

                // Terms & Conditions
                SettingsItem(
                    icon = Icons.Outlined.Description,
                    title = stringResource(R.string.terms_conditions_title),
                    onClick = {
                        openExternalLink(context, "https://www.LoveBeats.co/terms-and-conditions")
                    }
                )

                // Show logout and delete account only for authenticated users
                if (isUserAuthenticated) {
                    Spacer(modifier = Modifier.height(8.dp))

                    // Logout
                    SettingsItem(
                        icon = Icons.AutoMirrored.Filled.ExitToApp,
                        title = stringResource(R.string.logout),
                        onClick = {
                            handleLogout(context, navController)
                        }
                    )

                    Spacer(modifier = Modifier.height(8.dp))

                    // Delete Account (always last)
                    SettingsItem(
                        icon = Icons.Default.Delete,
                        title = stringResource(R.string.delete_account),
                        onClick = {
                            showDeleteDialog = true
                        }
                    )
                }
            }
        }
    }

    // Delete Account Confirmation Dialog
    if (showDeleteDialog) {
        AlertDialog(
            onDismissRequest = { showDeleteDialog = false },
            title = {
                Text(
                    text = stringResource(R.string.delete_account),
                    color = ImdbWhite
                )
            },
            text = {
                Text(
                    text = stringResource(R.string.delete_account_confirmation),
                    color = ImdbWhite
                )
            },
            confirmButton = {
                TextButton(
                    onClick = {
                        showDeleteDialog = false
                        handleDeleteAccount(context, navController)
                    }
                ) {
                    Text(
                        text = stringResource(R.string.delete),
                        color = ImdbYellow
                    )
                }
            },
            dismissButton = {
                TextButton(
                    onClick = { showDeleteDialog = false }
                ) {
                    Text(
                        text = stringResource(R.string.cancel),
                        color = ImdbWhite
                    )
                }
            },
            containerColor = ImdbBlack,
            titleContentColor = ImdbWhite,
            textContentColor = ImdbWhite
        )
    }
}

@Composable
private fun SettingsItem(
    icon: androidx.compose.ui.graphics.vector.ImageVector,
    title: String,
    onClick: () -> Unit
) {
    Card(
        modifier = Modifier
            .fillMaxWidth()
            .padding(horizontal = 16.dp)
            .clickable { onClick() },
        colors = CardDefaults.cardColors(containerColor = ImdbDarkGray),
        shape = RoundedCornerShape(8.dp)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(16.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            Icon(
                imageVector = icon,
                contentDescription = null,
                tint = ImdbWhite,
                modifier = Modifier.size(24.dp)
            )

            Spacer(modifier = Modifier.width(16.dp))

            Text(
                text = title,
                color = ImdbWhite,
                style = MaterialTheme.typography.bodyLarge,
                fontWeight = FontWeight.Medium,
                modifier = Modifier.weight(1f)
            )

            Icon(
                imageVector = Icons.AutoMirrored.Filled.KeyboardArrowRight,
                contentDescription = "Navigate",
                tint = ImdbWhite.copy(alpha = 0.6f),
                modifier = Modifier.size(20.dp)
            )
        }
    }
}

private fun openExternalLink(context: Context, url: String) {
    val intent = Intent(Intent.ACTION_VIEW, Uri.parse(url))
    context.startActivity(intent)
}

private fun handleLogout(context: Context, navController: NavController) {
    // Sign out from Firebase
    Firebase.auth.signOut()
    
    // Clean up app data
    AuthService.cleanup(context)
    
    // Navigate to login screen
    navController.navigate(NavRoutes.LOGIN) {
        popUpTo(navController.graph.startDestinationId) {
            inclusive = true
        }
        launchSingleTop = true
    }
}

private fun handleDeleteAccount(context: Context, navController: NavController) {
    val user = Firebase.auth.currentUser
    user?.delete()?.addOnCompleteListener { task ->
        if (task.isSuccessful) {
            // Clean up app data
            AuthService.cleanup(context)
            
            // Navigate to login screen
            navController.navigate(NavRoutes.LOGIN) {
                popUpTo(navController.graph.startDestinationId) {
                    inclusive = true
                }
                launchSingleTop = true
            }
        }
    }
}

@Preview(showBackground = true)
@Composable
fun SettingsScreenPreview() {
    val mockNavController = rememberNavController()
    SettingsScreen(
        navController = mockNavController,
        isUserAuthenticated = true
    )
}

@Preview(showBackground = true)
@Composable
fun SettingsScreenGuestPreview() {
    val mockNavController = rememberNavController()
    SettingsScreen(
        navController = mockNavController,
        isUserAuthenticated = false
    )
}
