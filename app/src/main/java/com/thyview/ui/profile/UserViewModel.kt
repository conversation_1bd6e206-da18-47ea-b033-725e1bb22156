package com.thyview.ui.profile

import androidx.lifecycle.ViewModel
import androidx.lifecycle.viewModelScope
import com.thyview.models.UpdateUserRequest
import com.thyview.models.User
import com.thyview.models.UsernameAvailabilityResponse
import com.thyview.models.DeleteUserResponse
import com.thyview.repositories.UserRepository
import dagger.hilt.android.lifecycle.HiltViewModel
import kotlinx.coroutines.flow.MutableStateFlow
import kotlinx.coroutines.flow.StateFlow
import kotlinx.coroutines.flow.asStateFlow
import kotlinx.coroutines.launch
import timber.log.Timber
import javax.inject.Inject

/**
 * ViewModel for user-related operations
 */
@HiltViewModel
class UserViewModel @Inject constructor(
    private val userRepository: UserRepository
) : ViewModel() {

    private val _currentUser = MutableStateFlow<User?>(null)
    val currentUser: StateFlow<User?> = _currentUser.asStateFlow()

    private val _isLoading = MutableStateFlow(false)
    val isLoading: StateFlow<Boolean> = _isLoading.asStateFlow()

    private val _error = MutableStateFlow<String?>(null)
    val error: StateFlow<String?> = _error.asStateFlow()

    private val _usernameAvailable = MutableStateFlow<Boolean?>(null)
    val usernameAvailable: StateFlow<Boolean?> = _usernameAvailable.asStateFlow()

    private val _profileImageUrl = MutableStateFlow<String?>(null)
    val profileImageUrl: StateFlow<String?> = _profileImageUrl.asStateFlow()

    // Track if profile image URL is being fetched to prevent duplicate calls
    private var isProfileImageUrlLoading = false

    /**
     * Get current user profile
     */
    fun getCurrentUser() {
        viewModelScope.launch {
            _isLoading.value = true
            _error.value = null

            val result = userRepository.getCurrentUser()
            result.fold(
                onSuccess = { user ->
                    _currentUser.value = user
                },
                onFailure = { exception ->
                    _error.value = "Failed to get user profile: ${exception.message}"
                    Timber.e(exception, "Failed to get current user")
                }
            )
            _isLoading.value = false
        }
    }

    /**
     * Refresh current user profile (useful after profile image updates)
     */
    fun refreshCurrentUser() {
        getCurrentUser()
    }

    /**
     * Update user profile
     */
    fun updateUser(request: UpdateUserRequest) {
        viewModelScope.launch {
            _isLoading.value = true
            _error.value = null

            val result = userRepository.updateUser(request)
            result.fold(
                onSuccess = { user ->
                    _currentUser.value = user
                    Timber.d("User updated successfully")
                },
                onFailure = { exception ->
                    _error.value = "Failed to update user: ${exception.message}"
                    Timber.e(exception, "Failed to update user")
                }
            )
            _isLoading.value = false
        }
    }

    /**
     * Check username availability
     */
    fun checkUsernameAvailability(username: String) {
        viewModelScope.launch {
            _error.value = null

            val result = userRepository.checkUsernameAvailability(username)
            result.fold(
                onSuccess = { available ->
                    _usernameAvailable.value = available
                },
                onFailure = { exception ->
                    _error.value = "Failed to check username availability: ${exception.message}"
                    _usernameAvailable.value = null
                    Timber.e(exception, "Failed to check username availability")
                }
            )
        }
    }

    /**
     * Delete current user
     */
    fun deleteUser() {
        viewModelScope.launch {
            _isLoading.value = true
            _error.value = null

            val result = userRepository.deleteUser()
            result.fold(
                onSuccess = { response ->
                    _currentUser.value = null
                    Timber.d("User deleted successfully: ${response.message}")
                },
                onFailure = { exception ->
                    _error.value = "Failed to delete user: ${exception.message}"
                    Timber.e(exception, "Failed to delete user")
                }
            )
            _isLoading.value = false
        }
    }

    /**
     * Clear error state
     */
    fun clearError() {
        _error.value = null
    }

    /**
     * Clear username availability state
     */
    fun clearUsernameAvailability() {
        _usernameAvailable.value = null
    }

    /**
     * Get profile image URL with duplicate call prevention
     */
    fun getProfileImageUrl() {
        if (isProfileImageUrlLoading) {
            Timber.d("Profile image URL fetch already in progress, skipping duplicate call")
            return
        }

        viewModelScope.launch {
            isProfileImageUrlLoading = true
            _error.value = null

            try {
                val result = userRepository.getProfileImageUrl()
                result.fold(
                    onSuccess = { url ->
                        _profileImageUrl.value = url
                        Timber.d("Profile image URL retrieved: $url")
                    },
                    onFailure = { exception ->
                        _error.value = "Failed to get profile image URL: ${exception.message}"
                        Timber.e(exception, "Failed to get profile image URL")
                    }
                )
            } finally {
                isProfileImageUrlLoading = false
            }
        }
    }

    /**
     * Clear profile image URL state
     */
    fun clearProfileImageUrl() {
        _profileImageUrl.value = null
    }

}