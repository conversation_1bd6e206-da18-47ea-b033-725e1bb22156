package com.thyview.ui.reviews

import androidx.compose.foundation.background
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.material.ExperimentalMaterialApi
import androidx.compose.material.pullrefresh.PullRefreshIndicator
import androidx.compose.material.pullrefresh.pullRefresh
import androidx.compose.material.pullrefresh.rememberPullRefreshState
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Modifier
import androidx.compose.ui.unit.dp
import androidx.compose.ui.Alignment
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import androidx.compose.ui.tooling.preview.Preview
import com.thyview.models.reviews.Post
import com.thyview.ui.NavRoutes
import com.thyview.ui.ThyViewTheme
import com.thyview.ui.ImdbBlack
import com.thyview.ui.ImdbYellow
import java.time.Instant

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun PostsScreen(
    navController: NavController,
    viewModel: PostsViewModel = hiltViewModel()
) {
    val state by viewModel.uiState.collectAsState()
    
    ThyViewTheme {
        Scaffold(
            containerColor = ImdbBlack,
            topBar = {
                TopAppBar(
                    title = { 
                        Text(
                            text = "Reviews", 
                            color = ImdbYellow
                        ) 
                    },
                    colors = TopAppBarDefaults.topAppBarColors(
                        containerColor = ImdbBlack,
                        titleContentColor = ImdbYellow
                    ),
                    windowInsets = WindowInsets(0.dp)
                )
            }
        ) { paddingValues ->
            PostsScreenContent(
                posts = state.posts,
                isLoading = state.isLoading,
                error = state.error,
                onLike = { viewModel.toggleLike(it) },
                onComment = { postId -> 
                    navController.navigate(NavRoutes.postDetail(postId))
                },
                onLoadMore = { viewModel.loadMorePosts() },
                onRefresh = { viewModel.refreshPosts() },
                modifier = Modifier.padding(top = paddingValues.calculateTopPadding())
            )
        }
    }
}

@OptIn(ExperimentalMaterialApi::class)
@Composable
fun PostsScreenContent(
    posts: List<Post>,
    isLoading: Boolean = false,
    error: String? = null,
    onLike: (String) -> Unit,
    onComment: (String) -> Unit,
    onLoadMore: () -> Unit,
    onRefresh: () -> Unit = {},
    modifier: Modifier = Modifier
) {
    val pullRefreshState = rememberPullRefreshState(
        refreshing = isLoading && posts.isEmpty(),
        onRefresh = onRefresh
    )
    
    Box(
        modifier = modifier
            .fillMaxSize()
            .background(ImdbBlack)
            .pullRefresh(pullRefreshState)
    ) {
        if (posts.isEmpty() && isLoading) {
            // Show loading state
            CircularProgressIndicator(
                modifier = Modifier.align(Alignment.Center),
                color = ImdbYellow
            )
        } else if (posts.isEmpty() && error != null) {
            // Show error state
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(16.dp),
                horizontalAlignment = Alignment.CenterHorizontally,
                verticalArrangement = Arrangement.Center
            ) {
                Text(
                    text = error,
                    color = MaterialTheme.colorScheme.error,
                    style = MaterialTheme.typography.bodyLarge
                )
                Spacer(modifier = Modifier.height(8.dp))
                Button(
                    onClick = onRefresh,
                    colors = ButtonDefaults.buttonColors(
                        containerColor = ImdbYellow,
                        contentColor = MaterialTheme.colorScheme.onPrimary
                    )
                ) {
                    Text("Try Again")
                }
            }
        } else {
            LazyColumn(
                modifier = Modifier.fillMaxSize()
            ) {
                items(posts) { post ->
                    PostItemCard(
                        post = post, 
                        onLike = { onLike(post.id) },
                        onComment = { onComment(post.id) }
                    )
                    
                    Spacer(modifier = Modifier.height(2.dp))
                }
                
                item {
                    if (isLoading && posts.isNotEmpty()) {
                        Box(
                            modifier = Modifier
                                .fillMaxWidth()
                                .padding(16.dp),
                            contentAlignment = Alignment.Center
                        ) {
                            CircularProgressIndicator(color = ImdbYellow)
                        }
                    } else {
                        // Load more when reaching the end
                        LaunchedEffect(Unit) {
                            onLoadMore()
                        }
                    }
                }
            }
        }
        
        // Pull to refresh indicator
        PullRefreshIndicator(
            refreshing = isLoading && posts.isEmpty(),
            state = pullRefreshState,
            modifier = Modifier.align(Alignment.TopCenter),
            backgroundColor = MaterialTheme.colorScheme.surface,
            contentColor = ImdbYellow
        )
    }
}

@Preview(showBackground = true, widthDp = 360)
@Composable
fun PostsScreenPreview() {
    // Using mock data for the preview
    ThyViewTheme {
        PostsScreenContent(
            posts = samplePosts,
            onLike = {},
            onComment = {},
            onLoadMore = {}
        )
    }
}

// Sample data for previews
private val samplePosts = listOf(
    Post(
        id = "1",
        externalAuthorId = "Jane Doe",
        username = "JaneDoe",
        profileImageUrl = "https://placekitten.com/100/100",
        title = "Doctor Review",
        rating = 4.5f,
        content = "I had a wonderful experience with Dr. Smith. Very knowledgeable about Hashimoto's.",
        imageUrl = null,
        likeCount = 24,
        commentCount = 5,
        viewCount = 100,
        createdAt = Instant.now().minusSeconds(7200).toString(),
        liked = true
    ),
    Post(
        id = "2",
        externalAuthorId = "John Smith",
        username = "JSmith",
        profileImageUrl = "https://placekitten.com/101/101",
        title = "Medication Review",
        rating = 5.0f,
        content = "My experience switching from Synthroid to Tirosint has been amazing. My symptoms have improved significantly.",
        imageUrl = "https://placekitten.com/400/300",
        likeCount = 42,
        commentCount = 12,
        viewCount = 210,
        createdAt = Instant.now().minusSeconds(3600).toString(),
        liked = false
    )
)
