package com.thyview.ui.search

import androidx.compose.foundation.background
import androidx.compose.foundation.clickable
import androidx.compose.foundation.layout.*
import androidx.compose.foundation.lazy.LazyColumn
import androidx.compose.foundation.lazy.items
import androidx.compose.foundation.shape.RoundedCornerShape
import androidx.compose.foundation.text.KeyboardActions
import androidx.compose.foundation.text.KeyboardOptions
import androidx.compose.material.icons.Icons
import androidx.compose.material.icons.filled.Clear
import androidx.compose.material.icons.filled.History
import androidx.compose.material.icons.filled.Search
import androidx.compose.material3.*
import androidx.compose.runtime.*
import androidx.compose.ui.Alignment
import androidx.compose.ui.Modifier
import androidx.compose.ui.draw.clip
import androidx.compose.ui.focus.FocusRequester
import androidx.compose.ui.focus.focusRequester
import androidx.compose.ui.platform.LocalFocusManager
import androidx.compose.ui.res.stringResource
import androidx.compose.ui.text.font.FontWeight
import androidx.compose.ui.text.input.ImeAction
import androidx.compose.ui.text.style.TextOverflow
import androidx.compose.ui.tooling.preview.Preview
import androidx.compose.ui.unit.dp
import androidx.compose.ui.unit.sp
import androidx.hilt.navigation.compose.hiltViewModel
import androidx.navigation.NavController
import androidx.navigation.compose.rememberNavController
import coil.compose.AsyncImage
import com.thyview.R
import com.thyview.models.search.SearchResult
import com.thyview.ui.ThyViewTheme
import com.thyview.ui.ImdbBlack
import com.thyview.ui.ImdbYellow
import com.thyview.ui.ImdbWhite
import com.thyview.ui.ImdbDarkGray
import com.thyview.ui.ImdbMediumGray

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SearchScreen(
    navController: NavController,
    viewModel: SearchViewModel = hiltViewModel()
) {
    val uiState by viewModel.uiState.collectAsState()

    ThyViewTheme {
        Scaffold(
            topBar = {
                TopAppBar(
                    title = {
                        Text(
                            text = stringResource(R.string.title_search),
                            color = ImdbYellow
                        )
                    },
                    colors = TopAppBarDefaults.topAppBarColors(
                        containerColor = ImdbBlack,
                        titleContentColor = ImdbYellow
                    ),
                    windowInsets = WindowInsets(0.dp)

                )
            },
            containerColor = ImdbBlack
        ) { innerPadding ->
            Column(
                modifier = Modifier
                    .fillMaxSize()
                    .padding(top = innerPadding.calculateTopPadding())
                    .background(ImdbBlack)
            ) {
                // Search bar
                SearchBar(
                    query = uiState.query,
                    onQueryChange = viewModel::updateQuery,
                    onSearch = viewModel::performSearch,
                    searchHistory = uiState.searchHistory,
                    onHistoryItemClick = viewModel::selectHistoryItem,
                    onClearHistory = viewModel::clearSearchHistory,
                    modifier = Modifier
                        .fillMaxWidth()
                        .padding(horizontal = 16.dp, vertical = 8.dp)
                )

                // Content area
                SearchContent(
                    uiState = uiState,
                    onResultClick = { result ->
                        when (result.type) {
                            "movie" -> navController.navigate("movie_details/${result.id}")
                            "tv" -> navController.navigate("tv_details/${result.id}")
                            "person" -> navController.navigate("person_details/${result.id}")
                            "genre" -> {
                                // For genre results, navigate to movie/TV details based on the actual content
                                if (result.title != null) {
                                    navController.navigate("movie_details/${result.id}")
                                } else if (result.name != null) {
                                    navController.navigate("tv_details/${result.id}")
                                }
                            }
                        }
                    },
                    modifier = Modifier.fillMaxSize()
                )
            }
        }
    }
}

@OptIn(ExperimentalMaterial3Api::class)
@Composable
fun SearchBar(
    query: String,
    onQueryChange: (String) -> Unit,
    onSearch: (String) -> Unit,
    searchHistory: List<String>,
    onHistoryItemClick: (String) -> Unit,
    onClearHistory: () -> Unit,
    modifier: Modifier = Modifier
) {
    val focusRequester = remember { FocusRequester() }
    val focusManager = LocalFocusManager.current
    var showHistory by remember { mutableStateOf(false) }

    Column(modifier = modifier) {
        OutlinedTextField(
            value = query,
            onValueChange = {
                onQueryChange(it)
                showHistory = it.isEmpty() && searchHistory.isNotEmpty()
            },
            modifier = Modifier
                .fillMaxWidth()
                .focusRequester(focusRequester),
            placeholder = { Text("Search movies, actors, or shows", color = ImdbWhite.copy(alpha = 0.6f)) },
            leadingIcon = {
                Icon(
                    imageVector = Icons.Default.Search,
                    contentDescription = "Search",
                    tint = ImdbYellow
                )
            },
            trailingIcon = {
                if (query.isNotEmpty()) {
                    IconButton(onClick = {
                        onQueryChange("")
                        showHistory = searchHistory.isNotEmpty()
                    }) {
                        Icon(
                            imageVector = Icons.Default.Clear,
                            contentDescription = "Clear search",
                            tint = ImdbWhite
                        )
                    }
                }
            },
            singleLine = true,
            shape = RoundedCornerShape(24.dp),
            colors = TextFieldDefaults.colors(
                focusedTextColor = ImdbWhite,
                unfocusedTextColor = ImdbWhite,
                cursorColor = ImdbYellow,
                focusedIndicatorColor = ImdbYellow,
                unfocusedIndicatorColor = ImdbMediumGray,
                focusedContainerColor = ImdbDarkGray,
                unfocusedContainerColor = ImdbDarkGray,
                focusedPlaceholderColor = ImdbWhite.copy(alpha = 0.6f),
                unfocusedPlaceholderColor = ImdbWhite.copy(alpha = 0.6f)
            ),
            keyboardOptions = KeyboardOptions(imeAction = ImeAction.Search),
            keyboardActions = KeyboardActions(
                onSearch = {
                    if (query.length >= 3) {
                        onSearch(query)
                        showHistory = false
                        focusManager.clearFocus()
                    }
                }
            )
        )

        // Search history dropdown
        if (showHistory && searchHistory.isNotEmpty()) {
            Card(
                modifier = Modifier
                    .fillMaxWidth()
                    .padding(top = 4.dp),
                colors = CardDefaults.cardColors(containerColor = ImdbDarkGray)
            ) {
                Column {
                    Row(
                        modifier = Modifier
                            .fillMaxWidth()
                            .padding(horizontal = 16.dp, vertical = 8.dp),
                        horizontalArrangement = Arrangement.SpaceBetween,
                        verticalAlignment = Alignment.CenterVertically
                    ) {
                        Text(
                            text = "Recent searches",
                            color = ImdbWhite,
                            fontSize = 14.sp,
                            fontWeight = FontWeight.Medium
                        )
                        TextButton(onClick = onClearHistory) {
                            Text(
                                text = "Clear all",
                                color = ImdbYellow,
                                fontSize = 12.sp
                            )
                        }
                    }

                    searchHistory.take(5).forEach { historyItem ->
                        Row(
                            modifier = Modifier
                                .fillMaxWidth()
                                .clickable {
                                    onHistoryItemClick(historyItem)
                                    showHistory = false
                                }
                                .padding(horizontal = 16.dp, vertical = 12.dp),
                            verticalAlignment = Alignment.CenterVertically
                        ) {
                            Icon(
                                imageVector = Icons.Default.History,
                                contentDescription = null,
                                tint = ImdbWhite.copy(alpha = 0.6f),
                                modifier = Modifier.size(16.dp)
                            )
                            Spacer(modifier = Modifier.width(12.dp))
                            Text(
                                text = historyItem,
                                color = ImdbWhite,
                                fontSize = 14.sp
                            )
                        }
                    }
                }
            }
        }
    }
}

@Composable
fun SearchContent(
    uiState: SearchUiState,
    onResultClick: (SearchResult) -> Unit,
    modifier: Modifier = Modifier
) {
    Box(modifier = modifier.background(ImdbBlack)) {
        when {
            uiState.isLoading -> {
                CircularProgressIndicator(
                    modifier = Modifier.align(Alignment.Center),
                    color = ImdbYellow
                )
            }

            uiState.error != null -> {
                Column(
                    modifier = Modifier.align(Alignment.Center),
                    horizontalAlignment = Alignment.CenterHorizontally
                ) {
                    Text(
                        text = "Technical error, try again later",
                        color = ImdbWhite,
                        fontSize = 16.sp
                    )
                    Spacer(modifier = Modifier.height(8.dp))
                    Text(
                        text = uiState.error,
                        color = ImdbWhite.copy(alpha = 0.7f),
                        fontSize = 12.sp
                    )
                }
            }

            uiState.searchResults.isNotEmpty() -> {
                LazyColumn(
                    modifier = Modifier.fillMaxSize(),
                    contentPadding = PaddingValues(vertical = 16.dp),
                    verticalArrangement = Arrangement.spacedBy(4.dp)
                ) {
                    items(uiState.searchResults) { result ->
                        SearchResultItem(
                            result = result,
                            onClick = { onResultClick(result) }
                        )
                    }
                }
            }

            uiState.query.isNotEmpty() && uiState.query.length >= 3 -> {
                Text(
                    text = "No results found for \"${uiState.query}\"",
                    color = ImdbWhite,
                    modifier = Modifier.align(Alignment.Center)
                )
            }

            else -> {
                Text(
                    text = "Enter at least 3 characters to search",
                    color = ImdbWhite.copy(alpha = 0.7f),
                    modifier = Modifier.align(Alignment.Center)
                )
            }
        }
    }
}

@Composable
fun SearchResultItem(
    result: SearchResult,
    onClick: () -> Unit,
    modifier: Modifier = Modifier
) {
    Card(
        modifier = modifier
            .fillMaxWidth()
            .clickable { onClick() },
        colors = CardDefaults.cardColors(containerColor = ImdbDarkGray)
    ) {
        Row(
            modifier = Modifier
                .fillMaxWidth()
                .padding(12.dp),
            verticalAlignment = Alignment.CenterVertically
        ) {
            // Poster/Profile image
            AsyncImage(
                model = result.poster ?: result.profile,
                contentDescription = null,
                modifier = Modifier
                    .size(width = 80.dp, height = 120.dp)
                    .clip(RoundedCornerShape(8.dp))
                    .background(ImdbMediumGray)
            )

            Spacer(modifier = Modifier.width(12.dp))

            Column(
                modifier = Modifier.weight(1f)
            ) {
                // Title/Name
                Text(
                    text = result.title ?: result.name ?: "Unknown",
                    color = ImdbWhite,
                    fontSize = 16.sp,
                    fontWeight = FontWeight.Medium,
                    maxLines = 1,
                    overflow = TextOverflow.Ellipsis
                )

                // Type and year
                Row(
                    verticalAlignment = Alignment.CenterVertically
                ) {
                    Text(
                        text = result.type.replaceFirstChar { it.uppercase() },
                        color = ImdbYellow,
                        fontSize = 12.sp,
                        fontWeight = FontWeight.Medium
                    )

                    result.releaseYear?.let { year ->
                        Text(
                            text = " • $year",
                            color = ImdbWhite.copy(alpha = 0.7f),
                            fontSize = 12.sp
                        )
                    }

                    result.rating?.let { rating ->
                        if (rating > 0) {
                            Text(
                                text = " • ★ ${String.format("%.1f", rating)}",
                                color = ImdbYellow,
                                fontSize = 12.sp
                            )
                        }
                    }
                }

                // Overview/Biography (for persons)
                val description = when (result.type) {
                    "person" -> result.knownForDepartment?.let { "$it" }
                    else -> result.overview
                }

                description?.let { desc ->
                    if (desc.isNotBlank()) {
                        Spacer(modifier = Modifier.height(4.dp))
                        Text(
                            text = desc,
                            color = ImdbWhite.copy(alpha = 0.8f),
                            fontSize = 12.sp,
                            maxLines = 2,
                            overflow = TextOverflow.Ellipsis
                        )
                    }
                }
            }
        }
    }
}

@Preview(showBackground = true, widthDp = 360)
@Composable
fun SearchScreenPreview() {
    // Using a mock NavController for the preview
    val mockNavController = rememberNavController()

    // Displaying the SearchScreen with the mock NavController
    ThyViewTheme {
        SearchScreen(navController = mockNavController)
    }
}
