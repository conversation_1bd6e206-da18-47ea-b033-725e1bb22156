<resources>

    <style name="AppTheme.NoActionBar" parent="AppTheme">
        <item name="windowActionBar">false</item>
        <item name="windowNoTitle">true</item>
        <item name="android:windowIsTranslucent">false</item>
        <item name="android:statusBarColor">@color/black</item>
        <item name="android:navigationBarColor">@color/black</item>
        <item name="android:windowLightStatusBar">false</item>
        <item name="android:windowLightNavigationBar">false</item>
        <item name="android:fitsSystemWindows">false</item>
        <item name="android:windowDrawsSystemBarBackgrounds">true</item>
    </style>

    <style name="RowSettingsNotificationsTwoViewHolderSwitchOnSwitchTheme">
        <item name="colorControlActivated">@color/color_primary</item>
    </style>

    <!-- Font Styles -->
    <style name="BaseTextStyle">
        <item name="android:textAllCaps">false</item>
    </style>

    <style name="BoldFont" parent="BaseTextStyle">
        <item name="android:fontFamily">@font/inter_bold</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@color/grey1</item>
    </style>

    <style name="MediumFont" parent="BaseTextStyle">
        <item name="android:fontFamily">@font/inter_medium</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@color/grey1</item>
    </style>

    <style name="RegularFont" parent="BaseTextStyle">
        <item name="android:fontFamily">@font/inter</item>
        <item name="android:textSize">16sp</item>
        <item name="android:textColor">@color/grey1</item>
    </style>

</resources>
